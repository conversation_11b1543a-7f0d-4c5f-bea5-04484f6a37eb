"use client";
import React, { Fragment, useContext } from "react";
import { DataContext } from "~/store/GlobalState";
import { groupMessagesByDate } from "~/utils/group-messages";
import InfiniteScroll from "react-infinite-scroll-component";
import UsePeopleMessage from "../../home/<USER>/hooks/people-message";
import Message from "../ChannelMessage/message";

const AgentMessage = () => {
  const { fetchMoreData, hasMore, loading } = UsePeopleMessage();
  const { state } = useContext(DataContext);
  const { chats } = state;
  const groupedMessages = groupMessagesByDate(chats);

  if (loading) return null;
  //

  return (
    <div
      id="scrollableDivs"
      style={{
        height: "100vh",
        overflowY: "scroll",
        display: "flex",
        flexDirection: "column-reverse",
      }}
      className="w-full pb-40"
    >
      <InfiniteScroll
        dataLength={chats?.length}
        next={fetchMoreData}
        hasMore={hasMore}
        loader={
          chats?.length !== 0 && (
            <h4 className="my-5 text-xs text-center">Loading threads...</h4>
          )
        }
        style={{
          display: "flex",
          flexDirection: "column-reverse",
          overflowY: "scroll",
        }}
        scrollableTarget="scrollableDivs"
        inverse={true}
      >
        {/* {agentState?.state === "working" && <ChatLoader />} */}
        {Object.entries(groupedMessages)?.map(([dateLabel, threads]: any) => (
          <Fragment key={dateLabel}>
            {threads?.map((item: any, index: number) => {
              const nextMessage = threads[index + 1];
              const shouldShowAvatar =
                !nextMessage || nextMessage.email !== item.email;

              return (
                <React.Fragment key={index}>
                  <Message item={item} shouldShowAvatar={shouldShowAvatar} />
                </React.Fragment>
              );
            })}

            <div className="relative my-2">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-dotted border-[#E6EAEF]"></div>
              </div>
              <div className="relative flex justify-center">
                <span className="bg-white px-4 py-1 text-[13px] text-[#101828] border border-[#E6EAEF] rounded-[30px]">
                  {dateLabel}
                </span>
              </div>
            </div>
          </Fragment>
        ))}
      </InfiniteScroll>
    </div>
  );
};

export default AgentMessage;
