"use client";

import Link from "next/link";
import { ChevronRight } from "lucide-react";
import ChannelDetailsDialog from "../channel-details-dialog";
import { useParams, useRouter } from "next/navigation";
import React, { useState, useRef, useEffect, useContext } from "react";
import { cn } from "~/lib/utils";
import NotificationSettingsModal from "../notification-modal";
import { ACTIONS } from "~/store/Actions";
import { DataContext } from "~/store/GlobalState";
import { PostRequest } from "~/utils/new-request";
import Loading from "~/components/ui/loading";

interface MenuDropdownProps {
  isOpen: boolean;
  onClose: () => void;
}

interface SubMenuState {
  copy: boolean;
}

const MenuDropdown = ({ isOpen, onClose }: MenuDropdownProps) => {
  const [subMenus, setSubMenus] = useState<SubMenuState>({
    copy: false,
  });
  const router = useRouter();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { state, dispatch } = useContext(DataContext);
  const [buttonLoading, setButtonLoading] = useState(false);
  const params = useParams();
  const id = params?.id as string;

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if click target is inside a dialog
      const isInsideDialog = (event.target as Element).closest(
        '[role="dialog"]'
      );
      if (isInsideDialog) return;

      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [onClose]);

  if (!isOpen) return null;

  const handleSubMenu = (menu: keyof SubMenuState) => {
    setSubMenus((prev) => ({
      ...prev,
      [menu]: !prev[menu],
    }));
  };

  const menuItemClass =
    "px-4 py-[10px] text-[15px] text-[#101828] hover:bg-[#F1F1FE] flex items-center justify-between cursor-pointer";
  const dividerClass = "h-px bg-[#E6EAEF]";

  // leave channel
  const handleLeave = async () => {
    setButtonLoading(true);

    const res = await PostRequest(`/channels/${id}/leave`, {});
    if (res?.status === 200 || res?.status === 201) {
      dispatch({ type: ACTIONS.CALLBACK, payload: !state?.callback });
      onClose();
    }
    setButtonLoading(false);
  };

  return (
    <div
      ref={dropdownRef}
      className="absolute top-full right-0 w-[220px] mt-2.5 bg-white rounded-[7px] shadow-lg border border-[#E6EAEF] z-20"
      onClick={() => dispatch({ type: ACTIONS.ACTIVE_TAB, payload: "about" })}
    >
      <div>
        <ChannelDetailsDialog className={cn(menuItemClass, "w-full")}>
          Open channel details
        </ChannelDetailsDialog>

        <div className={dividerClass} />

        <NotificationSettingsModal />

        <div
          className={menuItemClass}
          onClick={() => router.push("/workflows/new")}
        >
          Add a workflow
        </div>

        <div className={dividerClass} />

        <div
          className={`${menuItemClass} relative`}
          onClick={() => handleSubMenu("copy")}
          onMouseEnter={() => setSubMenus({ copy: true })}
          onMouseLeave={() => setSubMenus({ copy: false })}
        >
          Copy
          <ChevronRight className="w-4 h-4" color="#343330" />
          {subMenus.copy && (
            <div className="absolute right-full top-0 w-[200px] bg-white rounded-[7px] shadow-lg border border-[#E6EAEF]">
              <div className={menuItemClass}>Channel Link</div>
              <div className={menuItemClass}>Channel ID</div>
            </div>
          )}
        </div>

        <div
          className={menuItemClass}
          onClick={() => router.push("/channel/search")}
        >
          Search in channel
        </div>

        <Link
          href="/channel/new-window"
          target="_blank"
          className={menuItemClass}
        >
          Open in new window
        </Link>

        <div className={dividerClass} />

        <div
          className={`${menuItemClass} text-[#B00E03]`}
          onClick={handleLeave}
        >
          Leave channel{" "}
          {buttonLoading && <Loading color="red" height="15px" width="15px" />}
        </div>
      </div>
    </div>
  );
};

export default MenuDropdown;
