import { Check, Search } from "lucide-react";
import React, { useState } from "react";
import { Input } from "~/components/ui/input";
import images from "~/assets/images";
import Image from "next/image";
import { Button } from "~/components/ui/button";
import { useParams, useRouter } from "next/navigation";
import { PostRequest } from "~/utils/new-request";
import cogoToast from "cogo-toast";

interface AgentDropdownProps {
  isOpen: boolean;
  workflows: any;
}

const WorkflowDropdown = ({ isOpen, workflows }: AgentDropdownProps) => {
  const router = useRouter();
  const params = useParams();
  const channelId = params.id as string;
  const [searchInput, setSearchInput] = useState("");

  // Track active workflows
  const [selectedWorkflows, setSelectedWorkflows] = useState(
    workflows
      ?.filter((workflow: any) => workflow.is_active)
      .map((workflow: any) => workflow.id)
  );

  // Toggle workflow activation
  const toggleWorkflowActivation = async (workflow: any) => {
    const isActivating = !selectedWorkflows.includes(workflow?.id);
    setSelectedWorkflows((prev: any) =>
      isActivating
        ? [...prev, workflow?.id]
        : prev.filter((id: string) => id !== workflow?.id)
    );

    const payload = {
      workflow_id: workflow?.id,
      channel_id: channelId,
    };

    await PostRequest(`/channel-workflows`, payload);
    cogoToast.success("Workflow activated for this channel");
    // setCallback(!callback);
  };

  // Filter workflows by search
  const searchData = workflows?.filter((workflow: any) =>
    workflow.name.toLowerCase().includes(searchInput.toLowerCase())
  );

  if (!isOpen) return null;

  return (
    <div className="absolute z-50 top-full right-0 mt-2.5 w-[339px] bg-white rounded-[7px] shadow-lg border border-[#E6EAEF]">
      <div className="p-3 border-b border-[#E6EAEF] overflow-hidden">
        <div className="flex items-center p-[10px] gap-2 border border-[#E6EAEF] rounded-[6px] h-10">
          <Search className="w-5 h-5 text-[#667085]" />
          <Input
            placeholder="Find a workflow"
            className="w-full border-none p-0 h-full"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
          />
        </div>
      </div>

      {workflows.length === 0 ? (
        <div className="py-5 text-[15px] px-3 text-center">
          You have no workflows
        </div>
      ) : (
        <div className="pt-3 px-3 pb-[6px] max-h-[200px] overflow-y-auto">
          <h3 className="text-blue-500 font-semibold text-[13px] mb-2">
            Workflows
          </h3>

          {searchData?.map((workflow: any) => {
            const isActive = selectedWorkflows.includes(workflow.id);
            return (
              <div
                key={workflow.id}
                className="flex items-center justify-between py-[10px] hover:bg-gray-50 cursor-pointer"
                onClick={() => toggleWorkflowActivation(workflow)}
              >
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 rounded-sm border border-[#E6EAEF] flex items-center justify-center relative bg-green-100">
                    <Image
                      src={images?.blueBot}
                      alt={workflow.name}
                      width={20}
                      height={20}
                    />
                  </div>
                  <p className="text-[13px] font-semibold text-[#344054]">
                    {workflow.name}
                  </p>
                </div>
                <div
                  className={`w-5 h-5 rounded-full border ${
                    isActive
                      ? "border-[#8686F9] bg-[#8686F9]"
                      : "border-[#E6EAEF]"
                  } flex items-center justify-center`}
                >
                  {isActive && <Check className="w-3 h-3 text-white" />}
                </div>
              </div>
            );
          })}
        </div>
      )}

      <div className="bg-[#F6F7F9] p-3 flex items-center justify-between gap-3">
        <Button
          onClick={() => router.push("/client/workflows")}
          variant="outline"
          className="flex-1 h-9"
        >
          <span>Browse Workflows</span>
        </Button>

        <Button
          onClick={() => router.push("/client/workflows/new")}
          variant="default"
          className="flex-1 bg-[#7141F8] text-white h-9"
        >
          <span>Add New</span>
        </Button>
      </div>
    </div>
  );
};

export default WorkflowDropdown;
