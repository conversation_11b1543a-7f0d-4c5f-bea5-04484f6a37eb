"use client";

import {
  Pop<PERSON>,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { ChevronDownIcon, ChevronRight } from "lucide-react";
import { DataContext } from "~/store/GlobalState";
import { useContext, useState } from "react";
import Image from "next/image";
import { ACTIONS } from "~/store/Actions";
import { getInitials } from "~/utils/utils";
import Link from "next/link";

interface Props {
  name?: string;
}

export default function OrganisationMenu({ name }: Props) {
  const { state, dispatch } = useContext(DataContext);
  const { orgData } = state;
  const [subMenu, setSubMenu] = useState(false);

  const handleLogout = async () => {
    localStorage.clear();
    window.location.href = "/auth/login";
  };

  //

  return (
    <div className="">
      <Popover>
        <PopoverTrigger asChild>
          <div className="flex items-center gap-1 cursor-pointer">
            <h6 className="text-lg leading-[26px] font-semibold text-white">
              {name ? name : orgData?.name}
            </h6>
            <ChevronDownIcon className="text-white mt-1" />
          </div>
        </PopoverTrigger>

        <PopoverContent
          align="start"
          className="w-[270px] p-0 rounded-md shadow-xl"
          onClick={() =>
            dispatch({
              type: ACTIONS.CHANNEL_BAR,
              payload: !state?.channelBar,
            })
          }
        >
          <div className="">
            <div className="flex items-center gap-2 p-3 border-b font-medium text-sm">
              <div className="size-9 rounded border overflow-hidden flex items-center justify-center">
                {orgData?.logo_url ? (
                  <Image
                    src={orgData?.logo_url}
                    alt=""
                    width={50}
                    height={50}
                    unoptimized
                    className="size-9"
                  />
                ) : (
                  <h3 className="text-primary-500 font-bold text-sm">
                    {getInitials(orgData?.name)}
                  </h3>
                )}
              </div>

              <div className="text-sm">{orgData?.name}</div>
            </div>

            <div className="flex gap-2 text-xs px-3 py-3 font-medium border-b hover:bg-blue-500 hover:text-white cursor-pointer">
              🚀
              <div>
                Your <strong>Pro trial</strong> lasts through{" "}
                <strong>June 13</strong>.
                <a href="#" className="text-blue-600 block hover:underline">
                  See upgrade options
                </a>
              </div>
            </div>
          </div>

          <ul className="text-sm pb-3">
            <li
              onClick={() =>
                dispatch({ type: ACTIONS.INVITE_MODAL, payload: true })
              }
              className="hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 text-[15px]"
            >
              Invite people to {orgData?.name}
            </li>
            <li className="hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]">
              <Link href="/client/settings/personal/account">settings</Link>
            </li>
            <li
              className="relative hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]"
              onMouseEnter={() => setSubMenu(true)}
              onMouseLeave={() => setSubMenu(false)}
            >
              Apps <ChevronRight className="h-4 w-4" />
              {subMenu && (
                <ul className="absolute left-full top-0 w-[200px] bg-white text-black rounded-[7px] shadow-lg border border-[#E6EAEF] overflow-hidden">
                  <li className="hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]">
                    App1
                  </li>
                  <li className="hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]">
                    App2
                  </li>
                  <li className="hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]">
                    App3
                  </li>
                  <li className="hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]">
                    App4
                  </li>
                </ul>
              )}
            </li>
            <li
              onClick={handleLogout}
              className="hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 font-medium text-[15px]"
            >
              Sign out
            </li>
          </ul>
        </PopoverContent>
      </Popover>
    </div>
  );
}
