"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { Button } from "~/components/ui/button";
import { PlusIcon } from "lucide-react";

const WorkflowHeader = () => {
  const router = useRouter();

  const handleRoute = () => {
    router.push("/client/home/<USER>/new");
  };

  //

  return (
    <nav className="flex items-center justify-between bg-white px-6 py-4 border-b border-[#E6EAEF]">
      <h2 className="text-[#1D2939] text-base lg:text-lg font-bold">
        My Workflows
      </h2>

      <div className="flex items-center gap-3">
        {/* <Button variant="outline" className="border h-9" onClick={handleInvite}>
          <UserPlusIcon className="w-5 h-5" />
          <span className="ml-1 text-[13px] font-semibold">Invite People</span>
        </Button> */}

        <Button
          variant="outline"
          className="border-blue-50 h-9"
          onClick={handleRoute}
        >
          <PlusIcon className="w-5 h-5" color="#8686F9" />
          <span className="ml-1 text-[13px] font-semibold text-blue-200">
            Add new workflow
          </span>
        </Button>
      </div>
    </nav>
  );
};

export default WorkflowHeader;
