"use client";
import React from "react";
import CreateWorkflowDialog from "../workflows/create-workflow";

const NewWorkflowHeader = (agents: any) => {
  //

  return (
    <nav className="flex items-center justify-between bg-white px-6 py-4 border-b border-[#E6EAEF]">
      <div>
        <h2 className="text-[#1D2939] text-base lg:text-lg font-bold">
          Workflow Builder
        </h2>
        <p className="text-sm">
          Drag and drop agents to create your automated workflow
        </p>
      </div>

      <CreateWorkflowDialog agents={agents} />
    </nav>
  );
};

export default NewWorkflowHeader;
