import { useDraggable } from "@dnd-kit/core";
import Image from "next/image";
import images from "~/assets/images";

interface AgentCardProps {
  agent: any;
  isDragging?: boolean;
}

export function AgentCard({ agent, isDragging = false }: AgentCardProps) {
  const { attributes, listeners, setNodeRef, transform } = useDraggable({
    id: agent.id,
    data: agent,
  });

  const style = transform
    ? {
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
      }
    : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={`
        cursor-grab active:cursor-grabbing
        bg-workflow-agent-bg border border-workflow-agent-border
        rounded-lg p-4 transition-all duration-200
        hover:border-primary-300 hover:shadow-md
        ${isDragging ? "opacity-50 rotate-3 scale-105" : "opacity-100"}
        select-none
      `}
    >
      <div className="flex space-x-3">
        <Image
          src={agent?.app_logo || images?.bot}
          alt="image"
          width={36}
          height={36}
          className="object-cover rounded size-8 border"
          unoptimized
        />
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-sm text-foreground truncate">
            {agent?.app_name}
          </h3>
          <p className="text-xs text-muted-foreground truncate">
            {agent?.app_description}
          </p>
          <span className="inline-block mt-1 px-2 py-0.5 text-xs rounded-md bg-gray-100 text-secondary-foreground">
            {agent?.provider?.organization}
          </span>
        </div>
      </div>
    </div>
  );
}
