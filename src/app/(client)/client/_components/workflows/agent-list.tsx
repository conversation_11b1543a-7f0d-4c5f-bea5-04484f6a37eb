import { useState, useMemo } from "react";
import { Search } from "lucide-react";
import { Agent } from "~/types/workflow";
import { Input } from "~/components/ui/input";
import { AgentCard } from "./agent-card";

interface AgentsListProps {
  agents: Agent[];
  draggedAgent: string | null;
}

export function AgentsList({ agents, draggedAgent }: AgentsListProps) {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredAgents = useMemo(() => {
    return agents?.filter(
      (agent) =>
        agent.app_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        agent.app_description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [agents, searchTerm]);

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-border">
        <h2 className="text-lg font-semibold text-foreground mb-3">
          Available Agents
        </h2>
        <div className="relative">
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
            size={16}
          />
          <Input
            placeholder="Search agents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9"
          />
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-3">
          {filteredAgents.map((agent) => (
            <AgentCard
              key={agent.id}
              agent={agent}
              isDragging={draggedAgent === agent.id}
            />
          ))}
        </div>

        {filteredAgents.length === 0 && (
          <div className="text-center text-muted-foreground mt-8">
            <p>No agents found matching your search.</p>
          </div>
        )}
      </div>
    </div>
  );
}
