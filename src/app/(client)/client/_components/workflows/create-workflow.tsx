"use client";
import React, { useContext, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { XIcon } from "lucide-react";
import { PostRequest } from "~/utils/new-request";
import { ACTIONS } from "~/store/Actions";
import { DataContext } from "~/store/GlobalState";
import Loading from "~/components/ui/loading";
import Image from "next/image";
import images from "~/assets/images";
import cogoToast from "cogo-toast";

const CreateWorkflowDialog = ({ agents }: any) => {
  const [workflowName, setWorkflowName] = useState("");
  const [open, setOpen] = useState(false);
  const { state, dispatch } = useContext(DataContext);
  const { orgId } = state;
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: any) => {
    e.preventDefault();

    setLoading(true);

    const payload = {
      name: workflowName,
      description: " ",
      tags: [],
      meta: {
        additionalProp1: {},
      },
      agents_id: agents?.agents?.map((agent: any) => agent.agentId),
      connections: [
        {
          from: "",
          to: "",
          condition: "",
        },
      ],
    };

    const res = await PostRequest(`/organisations/${orgId}/workflows`, payload);
    if (res?.status === 200 || res?.status === 201) {
      dispatch({
        type: ACTIONS.WORKFLOW_CALLBACK,
        payload: !state?.workflowCallback,
      });
      cogoToast.success(res.data.message);
      setOpen(false);
    }

    setLoading(false);
  };

  //

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="bg-blue-100 h-9"
          disabled={agents?.agents?.length === 0}
        >
          <span className="ml-1 text-[13px] font-semibold text-white">
            Create workflow
          </span>
        </Button>
      </DialogTrigger>

      <DialogContent className="" aria-describedby="">
        <div className="flex flex-col border-[#E6EAEF] max-h-[450px]">
          <div className="flex items-center justify-between pb-4 mb-5 border-b border-[#E6EAEF]">
            <DialogTitle className="text-[#101828] font-black text-xl">
              Complete Workflow
            </DialogTitle>

            <div
              className="border rounded p-1 cursor-pointer"
              onClick={() => setOpen(false)}
            >
              <XIcon size={20} />
            </div>
          </div>

          <div className="overflow-y-auto pb-5">
            <form onSubmit={handleSubmit}>
              <div className="space-y-2 mb-6">
                <label
                  htmlFor="name"
                  className="block text-[#344054] text-sm font-medium"
                >
                  Give your workflow a name
                </label>
                <div className="relative">
                  <Input
                    id="name"
                    value={workflowName}
                    onChange={(e) =>
                      setWorkflowName(
                        e.target.value.replace(/\s/g, "-").toLowerCase()
                      )
                    }
                    placeholder="e.g. workflow-x"
                    className="text-[15px] pr-7"
                    maxLength={40}
                  />
                  <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-400">
                    {40 - workflowName.length}
                  </span>
                </div>
              </div>

              <div>
                <h3 className="text-[#101828] text-[17px] font-semibold">
                  Selected Agents
                </h3>

                {agents?.agents?.map((agent: any, index: number) => (
                  <div
                    className="flex space-x-3 border p-3 rounded-lg mt-5"
                    key={index}
                  >
                    <Image
                      src={agent?.app_logo || images?.bot}
                      alt="image"
                      width={36}
                      height={36}
                      className="object-cover rounded size-8 border"
                      unoptimized
                    />
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-sm text-foreground truncate">
                        {agent?.app_name}
                      </h3>
                      <p className="text-xs text-muted-foreground">
                        {agent?.app_description.length > 120
                          ? agent.app_description.slice(0, 120) + "..."
                          : agent?.app_description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </form>
          </div>

          <hr />

          <div className="flex items-end justify-end gap-4 pt-5 border-[#E6EAEF]">
            <Button
              variant="outline"
              type="button"
              onClick={() => setOpen(false)}
            >
              Cancel
            </Button>

            <Button
              type="submit"
              onClick={handleSubmit}
              disabled={workflowName === "" || loading}
              className="gap-1 bg-[#7141F9] hover:bg-[#5B2BF0] text-white"
            >
              Complete Workflow {loading && <Loading />}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateWorkflowDialog;
