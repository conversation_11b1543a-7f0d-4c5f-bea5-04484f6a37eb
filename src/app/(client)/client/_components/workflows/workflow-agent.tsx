import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Agent, WorkflowAgent as WorkflowAgentType } from "~/types/workflow";
import { X } from "lucide-react";
import Image from "next/image";
import images from "~/assets/images";

interface WorkflowAgentProps {
  workflowAgent: WorkflowAgentType;
  agent: Agent;
  onRemove: any;
}

export function WorkflowAgent({
  workflowAgent,
  agent,
  onRemove,
}: WorkflowAgentProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: workflowAgent.id,
    data: { workflowAgent, agent },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`
        group relative cursor-grab active:cursor-grabbing
        bg-primary-50 border border-workflow-agent-border
        rounded-lg p-4 transition-all duration-200
        hover:bg-workflow-agent-hover hover:shadow-md
        ${isDragging ? "opacity-50 z-50" : "opacity-100"}
        select-none
      `}
    >
      <button
        onClick={(e) => {
          e.stopPropagation();
          onRemove(workflowAgent.id);
        }}
        className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-destructive text-destructive-foreground rounded-full p-1 hover:bg-destructive/90"
      >
        <X size={12} />
      </button>

      <div className="flex items-center space-x-3">
        <Image
          src={agent?.app_logo || images?.bot}
          alt="image"
          width={36}
          height={36}
          className="object-cover rounded size-8 border"
          unoptimized
        />
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-sm text-foreground truncate">
            {agent?.app_name}
          </h3>
          <p className="text-xs text-muted-foreground truncate">
            {agent?.app_description}
          </p>
          <span className="inline-block mt-1 px-2 py-0.5 text-xs rounded-full bg-gray-100 text-secondary-foreground">
            {agent?.provider?.organization}
          </span>
        </div>
      </div>
    </div>
  );
}
