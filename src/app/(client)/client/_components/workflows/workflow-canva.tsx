import { useDroppable } from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { WorkflowAgent } from "./workflow-agent";
import { Agent, WorkflowAgent as WorkflowAgentType } from "~/types/workflow";
import { Plus } from "lucide-react";

interface WorkflowCanvasProps {
  workflowAgents: WorkflowAgentType[];
  agents: Agent[];
  onRemoveAgent: any;
  isOver: boolean;
}

export function WorkflowCanvas({
  workflowAgents,
  agents,
  onRemoveAgent,
}: WorkflowCanvasProps) {
  const { setNodeRef } = useDroppable({
    id: "workflow-canvas",
  });

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-border">
        <h2 className="text-lg font-semibold text-foreground">
          Workflow Canvas
        </h2>
        <p className="text-sm text-muted-foreground">
          Drag agents here to build your workflow
        </p>
      </div>

      <div
        ref={setNodeRef}
        className={`
          flex-1 p-4 transition-colors duration-200 pb-40 bg-primary-100
          ${workflowAgents.length === 0 ? "flex items-center justify-center" : ""}
        `}
        style={{ overflowY: "auto" }}
      >
        {workflowAgents.length === 0 ? (
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-primary-50 flex items-center justify-center">
              <Plus className="text-primary" size={24} />
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">
              Empty Workflow
            </h3>
            <p className="text-muted-foreground max-w-sm">
              Drag agents from the left panel to start building your workflow
            </p>
          </div>
        ) : (
          <SortableContext
            items={workflowAgents.map((wa) => wa.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-3">
              {workflowAgents.map((workflowAgent) => {
                const agent = agents.find(
                  (a) => a.id === workflowAgent.agentId
                );
                if (!agent) return null;

                return (
                  <WorkflowAgent
                    key={workflowAgent.id}
                    workflowAgent={workflowAgent}
                    agent={agent}
                    onRemove={onRemoveAgent}
                  />
                );
              })}
            </div>
          </SortableContext>
        )}
      </div>
    </div>
  );
}
