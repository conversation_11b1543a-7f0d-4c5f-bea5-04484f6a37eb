"use client";

import { <PERSON>R<PERSON>, PlusIcon } from "lucide-react";
import Image from "next/image";
import React, { useContext, useState } from "react";
import { DataContext } from "~/store/GlobalState";
import { search } from "~/utils/filter";
import images from "~/assets/images";
import { useRouter } from "next/navigation";
import WorkflowHeader from "../../_components/workflow-nav";
import { Button } from "~/components/ui/button";

export default function WorkflowsPage() {
  const { state } = useContext(DataContext);
  const { workflows } = state;
  const [searchInput, setSearchInput] = useState("");
  const router = useRouter();

  const mainWorkflow = search(workflows, searchInput);

  //

  return (
    <div className="flex flex-col h-[calc(100vh-70px)] relative w-full overflow-hidden">
      <WorkflowHeader />
      <div className="p-6 w-full overflow-y-auto">
        {/* Top Bar */}
        <div className="mb-4 flex items-center gap-3">
          <input
            type="text"
            placeholder="Find a workflow"
            className="w-full max-w-sm px-4 py-2 border rounded-md text-sm"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
          />
        </div>

        {/* Channel List */}
        {mainWorkflow?.map((workflow: any, index: number) => (
          <div
            key={index}
            className="flex items-center justify-between py-3 hover:bg-gray-50 transition-all group  border mb-4 cursor-pointer p-3 rounded-lg"
            // onClick={() => handleRoute(msg)}
          >
            <div className="flex gap-3 items-center">
              <div className="relative flex items-center justify-center size-8 rounded">
                <Image
                  src={workflow?.app_logo || images?.blueBot}
                  alt="image"
                  width={36}
                  height={36}
                  className="object-cover rounded size-8 border"
                  unoptimized
                />
              </div>

              <div className="text-sm">
                <div className="flex items-center flex-wrap gap-x-1">
                  <span className="font-semibold text-gray-800">
                    {workflow?.name}
                  </span>
                </div>
                {workflow?.description && (
                  <p className="text-gray-600 mt-0.5">
                    {workflow?.description}
                  </p>
                )}
              </div>
            </div>

            <div className="flex gap-2 items-center mt-1">
              <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-indigo-600" />
            </div>
          </div>
        ))}

        {mainWorkflow?.length === 0 && (
          <div className="block text-center mt-40 text-gray-400">
            <p>
              You have no workflow, click the button below to create new
              workflow
            </p>
            <Button
              variant="outline"
              className="border-blue-50 h-9 mt-5"
              onClick={() => router.push("/client/home/<USER>/new")}
            >
              <PlusIcon className="w-5 h-5" color="#8686F9" />
              <span className="ml-1 text-[13px] font-semibold text-blue-200">
                Add New
              </span>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
