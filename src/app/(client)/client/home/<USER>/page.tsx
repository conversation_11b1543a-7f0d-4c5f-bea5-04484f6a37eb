"use client";

import { ArrowR<PERSON>, ArrowDownUpIcon } from "lucide-react";
import Image from "next/image";
import React, { useContext, useState } from "react";
import { DataContext } from "~/store/GlobalState";
import { search } from "~/utils/filter";
import AllPeopleHeader from "../../_components/people-nav/all-peoples";
import images from "~/assets/images";
import { format } from "timeago.js";
import { useRouter } from "next/navigation";
import { PostRequest } from "~/utils/new-request";

export default function PeoplesPage() {
  const { state } = useContext(DataContext);
  const { orgMembers } = state;
  const [searchInput, setSearchInput] = useState("");
  const router = useRouter();

  const handleRoute = async (data: any) => {
    localStorage.setItem("channelName", data?.name);

    const orgId = localStorage.getItem("orgId") || "";

    const payload = {
      chat_type: data?.entity_type,
      participant_id: data?.id,
    };

    const res = await PostRequest(`/organisations/${orgId}/dms`, payload);

    if (res?.status === 200 || res?.status === 201) {
      router.push(
        `/client/people/${res?.data?.data?.channel_id}/${res?.data?.data?.participant_id}`
      );
    }
  };

  const dmsData = search(orgMembers, searchInput);

  //

  return (
    <div className="flex flex-col h-[calc(100vh-70px)] relative w-full overflow-hidden">
      <AllPeopleHeader />
      <div className="p-6 w-full overflow-y-auto">
        {/* Top Bar */}
        <div className="mb-4 flex items-center gap-3">
          <input
            type="text"
            placeholder="Find a person"
            className="w-full max-w-sm px-4 py-2 border rounded-md text-sm"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
          />

          <button className="p-2 border rounded-md text-gray-600 hover:bg-gray-50">
            <ArrowDownUpIcon className="text-gray-500" size={18} />
          </button>
        </div>

        {/* Channel List */}
        {dmsData?.map((msg: any, index: number) => (
          <div
            key={index}
            className="flex items-center justify-between py-3 hover:bg-gray-50 transition-all group  border mb-4 cursor-pointer p-3 rounded-lg"
            onClick={() => handleRoute(msg)}
          >
            <div className="flex gap-3 items-start">
              <div className="relative flex items-center justify-center size-8 rounded">
                <Image
                  src={
                    msg?.profile_url
                      ? msg?.profile_url
                      : msg?.entity_type === "user" || msg?.entity_type === ""
                        ? images?.user
                        : images?.bot
                  }
                  alt="image"
                  width={36}
                  height={36}
                  className="object-cover rounded size-8 border"
                  unoptimized
                />

                <div className="absolute -bottom-1 -right-1 z-40 bg-green-500 h-[7px] w-[7px] border border-white rounded-full" />
              </div>

              <div className="text-sm">
                <div className="flex items-center flex-wrap gap-x-1">
                  <span className="font-semibold text-gray-800">
                    {msg.name}
                  </span>

                  <span className="text-gray-400 ml-2 text-xs">
                    {format(msg.last_read_at)}
                  </span>
                </div>
                <p className="text-gray-600 mt-0.5">{msg.email}</p>
              </div>
            </div>

            <div className="flex gap-2 items-center mt-1">
              {/* {msg.unread && (
                <div className="text-white bg-indigo-600 text-xs px-2 py-1 rounded-full leading-none font-medium">
                  {msg.unread}
                </div>
              )} */}
              <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-indigo-600" />
            </div>
          </div>
        ))}

        {dmsData?.length === 0 && (
          <p className="text-center mt-40 text-gray-400">
            People not available
          </p>
        )}
      </div>
    </div>
  );
}
