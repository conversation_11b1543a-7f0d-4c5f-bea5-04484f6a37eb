"use client";
import { useRouter } from "next/navigation";
import React, { useContext, useEffect, useState } from "react";
import { ACTIONS } from "~/store/Actions";
import { DataContext } from "~/store/GlobalState";
import Topbar from "~/components/layout/topbar";
import SideBar from "~/components/layout/sidebar";
import RecentMessages from "./hooks/recent-messages";
import { GetRequest } from "~/utils/new-request";
import UserSidebar from "./_components/profile-sidebar/user-sidebar";
import { useSubscriptionPlans } from "~/hooks/useSubscriptionPlans";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { state, dispatch } = useContext(DataContext);

  // Fetch subscription plans when app loads
  useSubscriptionPlans();

  useEffect(() => {
    const token = localStorage.getItem("token");

    if (!token) {
      router.push("/auth/login");
      return;
    }
  }, [router]);

  useEffect(() => {
    const token = localStorage.getItem("token");
    const orgId = localStorage.getItem("orgId");

    dispatch({ type: ACTIONS.TOKEN, payload: token });
    dispatch({ type: ACTIONS.ORG_ID, payload: orgId });

    const getCurrentSubscription = async () => {
      const response = await GetRequest(`/subscriptions/current/${orgId}`);
      if (response?.status === 200 || response?.status === 201) {
        dispatch({
          type: ACTIONS.CURRENT_SUBCRIPTION,
          payload: response?.data?.data,
        });
      }
    };

    getCurrentSubscription();
  }, [dispatch, router]);

  // get users profile
  useEffect(() => {
    const getUser = async () => {
      const response = await GetRequest(`/profile`);
      if (response?.status === 200 || response?.status === 201) {
        dispatch({ type: ACTIONS.USER, payload: response?.data?.data });
      }
    };

    getUser();
  }, [dispatch, state?.profileCallback]);

  // get all users in an organisation
  useEffect(() => {
    const orgId = localStorage.getItem("orgId");

    const getOrganisationUsers = async () => {
      const response = await GetRequest(
        `/organisations/${orgId}/users?page=1&limit=50`
      );

      if (response?.status === 200 || response?.status === 201) {
        dispatch({ type: ACTIONS.ORG_MEMBERS, payload: response?.data?.data });
      }
      setLoading(false);
    };

    const getOrganisationInvites = async () => {
      const response = await GetRequest(`/organisations/${orgId}/invites`);
      if (response?.status === 200 || response?.status === 201) {
        const result = response.data.data.filter(
          (item: any) => item.status === "invited"
        );
        dispatch({ type: ACTIONS.ORG_INVITES, payload: result });
      }
    };

    getOrganisationUsers();
    getOrganisationInvites();
  }, [dispatch]);

  if (loading) return null;

  return (
    <>
      <RecentMessages />
      <Topbar />

      <div className="w-full flex relative">
        <SideBar />
        <div className={`w-full relative`}>
          {children}

          <UserSidebar />
        </div>
      </div>
    </>
  );
}
