"use client";

import { useContext, useEffect, useState } from "react";
import { Search, ChevronRight } from "lucide-react";
import Image from "next/image";
import PageHeader from "../../components/page-header";
import { DataContext } from "~/store/GlobalState";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import images from "~/assets/images";
import { Switch } from "~/components/ui/switch";
import { GetRequest } from "~/utils/new-request";
import Loading from "~/components/ui/loading";

export default function AgentProfile() {
  const [activeTab, setActiveTab] = useState("Details");
  const { state } = useContext(DataContext);
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;
  const [loading, setLoading] = useState(true);
  const searchParams = useSearchParams();
  const json = searchParams.get("json_url") || "";
  const [isActive, setIsActive] = useState(false);
  const [workflow, setWorkflow] = useState<any>(null);

  // get single workflow
  useEffect(() => {
    const orgId = localStorage.getItem("orgId") || "";

    if (id) {
      const getWorkflows = async () => {
        const res = await GetRequest(`/organisations/${orgId}/workflows/${id}`);
        setWorkflow(res.data.data);
        setLoading(false);
      };
      getWorkflows();
    }
  }, [json, id]);

  // Function to handle switch change
  const handleSwitchChange = async (checked: boolean, id: string) => {
    // const orgId = localStorage.getItem("orgId") || "";

    // const payload = {
    //   integration_id: id,
    //   status: checked,
    // };

    setIsActive(checked);
    console.log(id);

    // const res = await PatchRequest(
    //   `/organisations/${orgId}/agents/change_status`,
    //   payload
    // );

    // if (res?.status === 200 || res?.status === 201) {
    //   setIsActive(checked);
    //   setWorkflow((prev: any) => ({
    //     ...prev,
    //     is_active: checked,
    //   }));
    //   dispatch({ type: ACTIONS.CALLBACK, payload: !state?.callback });
    // }
  };

  const TABS = [
    "Details",
    ...(isActive ? ["Settings", "Output"] : []),
    "Usage",
    ...(isActive ? ["Key"] : []),
  ];

  //

  return (
    <>
      <PageHeader
        title="Workflow Profile"
        buttonIcon={
          <Search
            size={20}
            className="text-gray-600 cursor-pointer hover:text-gray-800"
          />
        }
      />

      {loading ? (
        <div className="flex items-center justify-center mt-20">
          <Loading width="40" height="40" color="blue" />
        </div>
      ) : (
        <>
          <div className="flex flex-wrap items-center gap-3 text-sm py-3 border-b px-4 md:px-5">
            <span
              onClick={() => router.back()}
              className="text-[#667085] cursor-pointer"
            >
              {state?.topLabel} Workflows
            </span>
            <ChevronRight />
            <span className="break-all">
              {workflow?.descriptions?.app_name}
            </span>
          </div>

          <div className="bg-white p-4 md:p-6 mx-auto">
            {/* Header */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
              <div className="flex flex-col sm:flex-row items-center gap-4 w-full md:w-auto">
                <div className="w-[100px] h-[100px] min-w-[100px] border rounded-xl">
                  <Image
                    src={
                      workflow?.descriptions?.app_logo || "/image/bluebot.svg"
                    }
                    alt={workflow?.descriptions?.app_name}
                    width={100}
                    height={100}
                    className="rounded-xl bg-green-100"
                  />
                </div>

                <div className="w-full">
                  <h2 className="text-lg font-semibold flex items-center gap-2 flex-wrap">
                    {workflow?.name}
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">
                    {workflow?.description || "Workflow description"}
                  </p>
                </div>
              </div>

              <div
                className={`flex items-center gap-2 rounded-3xl border ${workflow?.is_active ? "border-[#7141F8]" : ""} py-5 px-3 h-0`}
              >
                <span
                  className={`text-sm ${workflow?.is_active ? "text-[#7141F8]" : "text-[#667085]"}`}
                >
                  {workflow?.is_active ? "Enabled" : "Disabled"}
                </span>

                <Switch
                  className="bg-green-500"
                  checked={isActive}
                  onCheckedChange={(checked) => {
                    handleSwitchChange(checked, id);
                  }}
                />
              </div>
            </div>

            {/* Tabs */}
            <div className="mt-8 border-b border-gray-200 overflow-x-auto">
              <div className="flex gap-4 min-w-max">
                {TABS.map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`px-3 py-2 text-sm font-medium whitespace-nowrap ${
                      activeTab === tab
                        ? "text-indigo-600 border-b-2 border-indigo-600"
                        : "text-gray-500 hover:text-gray-700"
                    }`}
                  >
                    {tab}
                  </button>
                ))}
              </div>
            </div>

            {/* Tab Content */}
            {activeTab === "Details" && (
              <div className="mt-6">
                <h2 className="font-semibold text-sm text-gray-800 mb-4">
                  Agents that works with {workflow?.name}
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-5 items-start">
                  {workflow?.agents_details?.map(
                    (agent: any, index: number) => {
                      return (
                        <div
                          key={index}
                          className={`transition-all duration-300 border rounded-xl bg-white shadow-sm hover:border-[#D0D0FD] cursor-pointer`}
                        >
                          {/* Header */}
                          <div
                            // onClick={() => handleRoute(agent)}
                            className="flex items-center justify-between p-3 border-b"
                          >
                            <div className="flex items-center gap-3">
                              <div
                                className={`relative h-9 w-9 rounded-lg flex items-center justify-center border border-[#E6EAEF]`}
                              >
                                <Image
                                  src={agent?.app_logo || images.blueBot}
                                  alt="Bot"
                                  width={20}
                                  height={20}
                                  className="h-8 w-8"
                                />
                                <div className="absolute -bottom-[3px] -right-[3px] w-[10px] h-[10px] bg-[#6DC347] rounded-full border border-white" />
                              </div>
                              <div>
                                <h2 className="text-sm font-semibold text-[#344054]">
                                  {agent.name}
                                </h2>
                              </div>
                            </div>
                            <div className="text-xs text-[#667085] border border-[#D0D5DD] rounded-full px-2 py-[2px]">
                              {index + 1}
                            </div>
                          </div>

                          {/* <div>
                          <p className="mt-2 text-sm text-[#475467] px-3 ">
                            {agent.app_description}
                          </p>

                          <div
                            className={`transition-[max-height] duration-300 ease-in-out overflow-hidden mt-3 ${isActive ? "max-h-[500px]" : "max-h-0"
                              }`}
                          >
                            <div className="mt-4 py-3 border-t text-xs text-[#667085] text-center">
                              Used by 30+ companies in simplifying their workflow
                            </div>
                          </div>
                        </div> */}
                        </div>
                      );
                    }
                  )}
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </>
  );
}
