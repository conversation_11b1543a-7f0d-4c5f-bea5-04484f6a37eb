"use client";

import React from "react";
import GeneralNotificationConnection from "~/components/layout/centrifugo/general-notification-connection";
import WorkflowNav from "~/components/layout/sidebar/workflow-nav";

interface LayoutProps {
  children: React.ReactNode;
}
function Layout({ children }: LayoutProps) {
  return (
    <div className="w-full flex relative">
      <GeneralNotificationConnection />
      <WorkflowNav />

      <div className={`w-full lg:ml-[495px] mt-[60px] relative`}>
        {children}
      </div>
    </div>
  );
}

export default Layout;
