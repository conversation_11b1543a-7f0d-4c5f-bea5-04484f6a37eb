"use client";
import axios from "axios";
import cogoToast from "cogo-toast";
import React, { useState } from "react";

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  company?: string;
  product: string;
}

const ProductInterestForm = ({ product }: { product: string }) => {
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState<FormData>({
    firstName: "",
    lastName: "",
    email: "",
    company: "",
    product: product,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const isFormValid = form.firstName && form.lastName && form.email;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isFormValid) {
      cogoToast.error("Please fill in all required fields!");
      return;
    }
    setLoading(true);

    const currentDateTime = new Date().toLocaleString();
    const message = `New product interest submission from ${form.firstName} ${form.lastName} (${form.email}) for ${product.toUpperCase()} ${form.company ? ` from ${form.company}` : ""} at ${currentDateTime}`;

    try {
      await axios
        .post(
          "https://ping.telex.im/v1/webhooks/01983c9f-af0b-745c-b141-6849d270aabe",
          {
            username: `${form.firstName} ${form.lastName}`,
            event_name: "testing",
            status: "success",
            message: message,
          }
        )
        .then(() => {
          cogoToast.success("Form submitted successfully!");
        });
      setLoading(false);
    } catch (error) {
      console.error("Error submitting form:", error);
      cogoToast.error("Something went wrong. Please try again.");
      setLoading(false);
    }
  };

  return (
    <form
      action=""
      onSubmit={handleSubmit}
      className="flex flex-col gap-4 bg-white p-5 rounded-md shadow-md w-full lg:w-fit min-w-[400px]"
    >
      <div className="flex gap-4">
        <div className="flex flex-1 flex-col gap-1 text-sm">
          <label htmlFor="firstName">First Name</label>
          <input
            type="text"
            name="firstName"
            placeholder="John"
            className="border px-2 py-3 rounded-lg outline-none w-full"
            onChange={handleChange}
            value={form.firstName}
          />
        </div>
        <div className="flex flex-1 flex-col gap-1 text-sm">
          <label htmlFor="lastName">Last Name</label>
          <input
            type="text"
            name="lastName"
            placeholder="Doe"
            className="border px-2 py-3 rounded-lg outline-none w-full"
            onChange={handleChange}
            value={form.lastName}
          />
        </div>
      </div>
      <div className="flex flex-col gap-1 text-sm">
        <label htmlFor="email">Email</label>
        <input
          type="email"
          name="email"
          placeholder="<EMAIL>"
          className="border px-2 py-3 rounded-lg outline-none w-full"
          onChange={handleChange}
          value={form.email}
        />
      </div>
      <div className="flex flex-col gap-1 text-sm">
        <label htmlFor="company">Company Name (Optional)</label>
        <input
          type="text"
          name="company"
          placeholder="JK Holdings"
          className="border px-2 py-3 rounded-lg outline-none w-full"
          onChange={handleChange}
          value={form.company}
        />
      </div>
      <button
        disabled={!isFormValid || loading}
        className={`bg-[#8860F8] text-sm text-white py-3 px-4 rounded-md items-start max-w-36 ml-auto transition-opacity duration-200 ${!isFormValid ? "opacity-60 cursor-not-allowed" : "opacity-100"}`}
      >
        {loading ? "Loading..." : "Submit Interest"}
      </button>
    </form>
  );
};

export default ProductInterestForm;
