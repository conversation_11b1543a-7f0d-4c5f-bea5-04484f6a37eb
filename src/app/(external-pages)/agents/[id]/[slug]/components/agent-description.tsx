"use client";
import React from "react";
import { MapPin } from "lucide-react";
import { Agent } from "../../../page";

interface AgentDescriptionProps {
  agent: Agent;
}

const AgentDescription = ({ agent }: AgentDescriptionProps) => {
  return (
    <div className="space-y-8">
      {/* What is this Agent section */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <MapPin className="text-red-500" size={20} />
          <h2 className="text-xl font-semibold text-gray-900">
            What is {agent.app_name}?
          </h2>
        </div>
        
        <div className="prose max-w-none">
          <p className="text-gray-700 mb-4">
            {agent.app_description}
          </p>
          
          <div className="space-y-4">
            <div>
              <strong className="text-gray-900">Generate qualified leads:</strong> extract business names, websites, emails, and phone numbers to build prospect lists for your sales team
            </div>
            
            <div>
              <strong className="text-gray-900">Track competitors across regions:</strong> monitor where competitors operate, how they're rated, and how many reviews they've received
            </div>
            
            <div>
              <strong className="text-gray-900">Perform market analysis:</strong> analyze market saturation, identify service gaps, or benchmark local businesses by size, rating, and visibility
            </div>
            
            <div>
              <strong className="text-gray-900">Support partnerships:</strong> discover top-rated or high-volume locations for outreach and collaboration
            </div>
            
            <div>
              <strong className="text-gray-900">Automate research workflows:</strong> replace manual search tasks with repeatable, workflows that keep datasets fresh and consistent.
            </div>
          </div>
          
          <p className="text-gray-700 mt-4">
            The scraper expands Google Maps data extraction beyond the limitations of the official{" "}
            <a href="#" className="text-blue-600 hover:underline">Google Places API</a> and bypasses the{" "}
            <a href="#" className="text-blue-600 hover:underline">limitation of Google Maps</a> of displaying (and scraping) no more than 120 places per area.
          </p>
        </div>
      </div>

      {/* What data does Agent extract section */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-6">
          What data does {agent.app_name} extract?
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Data extraction items */}
          <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-gray-900">Title/place name</div>
            </div>
          </div>
          
          <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-gray-900">Subtitle, category, place ID, and URL</div>
            </div>
          </div>
          
          <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-gray-900">Address</div>
            </div>
          </div>
          
          <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-gray-900">Location, plus code and exact coordinates</div>
            </div>
          </div>
          
          <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-gray-900">Phone number</div>
            </div>
          </div>
          
          <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-gray-900">Website, if available</div>
            </div>
          </div>
          
          <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-pink-400 rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-gray-900">Company contact details from website (company email, phone number and social media profiles)</div>
            </div>
          </div>
          
          <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-gray-900">Business leads enrichment (full name, work email address, phone number, job title, LinkedIn profile)</div>
            </div>
          </div>
          
          <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-blue-300 rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-gray-900">Search results</div>
            </div>
          </div>
          
          <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-gray-600 rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-gray-900">Review count and review distribution</div>
            </div>
          </div>
          
          <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-gray-900">Average rating (totalScore)</div>
            </div>
          </div>
          
          <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-gray-700 rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-gray-900">List of images</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentDescription;
