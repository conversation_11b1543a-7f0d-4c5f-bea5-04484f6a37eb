"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import { ChevronLeft, Star } from "lucide-react";
import { Agent } from "../../../page";

interface AgentHeaderProps {
  agent: Agent;
}

const AgentHeader = ({ agent }: AgentHeaderProps) => {
  return (
    <div>
      {/* Breadcrumb */}
      <div className="flex items-center gap-2 text-sm text-gray-600 mb-6">
        <Link 
          href="/agents" 
          className="flex items-center gap-1 hover:text-gray-900 transition-colors"
        >
          <ChevronLeft size={16} />
          Go to Store
        </Link>
      </div>

      {/* Agent Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-4">
          {/* Agent Logo */}
          <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
            {agent.app_logo ? (
              <Image
                src={agent.app_logo}
                alt={agent.app_name}
                width={64}
                height={64}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold text-sm">
                  {agent.app_name.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
          </div>

          {/* Agent Info */}
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {agent.app_name}
            </h1>
            
            {/* Provider Info */}
            <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
              <span>
                Developed by{" "}
                <span className="font-medium text-gray-900">
                  {agent.provider?.organization || "Telex"}
                </span>
              </span>
              <span>Maintained daily</span>
            </div>

            {/* Description */}
            <p className="text-gray-700 mb-4 max-w-2xl">
              {agent.app_description}
            </p>

            {/* Stats */}
            <div className="flex items-center gap-6 text-sm">
              <div className="flex items-center gap-1">
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      size={16}
                      className="text-yellow-400 fill-current"
                    />
                  ))}
                </div>
                <span className="font-medium">5.0 (93)</span>
              </div>
              
              <div className="flex items-center gap-1">
                <span className="text-gray-600">Pricing:</span>
                <span className="font-medium">Pay per event</span>
              </div>
              
              <div className="flex items-center gap-1">
                <span className="text-gray-600">Total runs:</span>
                <span className="font-medium">150K</span>
              </div>
              
              <div className="flex items-center gap-1">
                <span className="text-gray-600">Runs success:</span>
                <span className="font-medium text-green-600">97%</span>
              </div>
              
              <div className="flex items-center gap-1">
                <span className="text-gray-600">Used over:</span>
                <span className="font-medium">4.3 days</span>
              </div>
            </div>

            {/* Last modified */}
            <div className="text-xs text-gray-500 mt-2">
              Last modified 18 hours ago
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-3">
          <span className="text-sm text-blue-600 cursor-pointer hover:underline">
            Use this Agent in Telex
          </span>
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
            Try for free
          </button>
        </div>
      </div>
    </div>
  );
};

export default AgentHeader;
