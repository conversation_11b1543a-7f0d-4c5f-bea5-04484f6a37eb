"use client";
import React from "react";
import { <PERSON>hare2, <PERSON><PERSON>, FileText, HelpCircle, BarChart3 } from "lucide-react";
import { Agent } from "../../../page";

interface AgentSidebarProps {
  agent: Agent;
}

const AgentSidebar = ({ agent }: AgentSidebarProps) => {
  return (
    <div className="space-y-6">
      {/* On this page section */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-semibold text-gray-900 mb-3">On this page</h3>
        <ul className="space-y-2 text-sm">
          <li>
            <a href="#what-is" className="text-blue-600 hover:underline">
              What is {agent.app_name}?
            </a>
          </li>
          <li>
            <a href="#data-extract" className="text-blue-600 hover:underline">
              What data does {agent.app_name} extract?
            </a>
          </li>
        </ul>
      </div>

      {/* Input section */}
      <div className="border rounded-lg p-4">
        <div className="flex items-center gap-2 mb-3">
          <div className="w-4 h-4 bg-blue-500 rounded"></div>
          <span className="font-medium text-gray-900">Input</span>
        </div>
        <p className="text-sm text-gray-600">
          Using geolocation or pinpoint accuracy
        </p>
      </div>

      {/* Output section */}
      <div className="border rounded-lg p-4">
        <div className="flex items-center gap-2 mb-3">
          <div className="w-4 h-4 bg-blue-500 rounded"></div>
          <span className="font-medium text-gray-900">Output</span>
        </div>
      </div>

      {/* FAQ section */}
      <div className="border rounded-lg p-4">
        <div className="flex items-center gap-2 mb-3">
          <HelpCircle className="text-gray-600" size={16} />
          <span className="font-medium text-gray-900">FAQ</span>
        </div>
      </div>

      {/* Share Actor section */}
      <div className="border rounded-lg p-4">
        <h3 className="font-semibold text-gray-900 mb-3">Share Actor</h3>
        <div className="flex items-center gap-2">
          <Share2 size={16} className="text-gray-600" />
          <Copy size={16} className="text-gray-600" />
          <FileText size={16} className="text-gray-600" />
          <BarChart3 size={16} className="text-gray-600" />
        </div>
      </div>

      {/* Version info */}
      <div className="text-xs text-gray-500 space-y-1">
        <div>Version: {agent.version || "1.0.0"}</div>
        <div>Created: {new Date(agent.created_at).toLocaleDateString()}</div>
        <div>Updated: {new Date(agent.updated_at).toLocaleDateString()}</div>
      </div>
    </div>
  );
};

export default AgentSidebar;
