"use client";
import React, { useState } from "react";
import { Agent } from "../../../page";
import AgentHeader from "./agent-header";
import AgentTabs from "./agent-tabs";
import AgentDescription from "./agent-description";
import AgentSidebar from "./agent-sidebar";

interface SingleAgentClientProps {
  agent: Agent;
}

const SingleAgentClient = ({ agent }: SingleAgentClientProps) => {
  const [activeTab, setActiveTab] = useState("Description");

  const tabs = [
    "Lead generation",
    "Description", 
    "README",
    "Pricing",
    "API",
    "Issues",
    "Changelog"
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case "Description":
        return <AgentDescription agent={agent} />;
      case "README":
        return <div className="p-6">README content coming soon...</div>;
      case "Pricing":
        return <div className="p-6">Pricing information coming soon...</div>;
      case "API":
        return <div className="p-6">API documentation coming soon...</div>;
      case "Issues":
        return <div className="p-6">Issues tracking coming soon...</div>;
      case "Changelog":
        return <div className="p-6">Changelog coming soon...</div>;
      case "Lead generation":
        return <div className="p-6">Lead generation features coming soon...</div>;
      default:
        return <AgentDescription agent={agent} />;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <AgentHeader agent={agent} />
      
      {/* Main Content */}
      <div className="flex flex-col lg:flex-row gap-8 mt-8">
        {/* Left Content */}
        <div className="flex-1">
          {/* Tabs */}
          <AgentTabs 
            tabs={tabs} 
            activeTab={activeTab} 
            onTabChange={setActiveTab} 
          />
          
          {/* Tab Content */}
          <div className="mt-6">
            {renderTabContent()}
          </div>
        </div>
        
        {/* Right Sidebar */}
        <div className="lg:w-80">
          <AgentSidebar agent={agent} />
        </div>
      </div>
    </div>
  );
};

export default SingleAgentClient;
