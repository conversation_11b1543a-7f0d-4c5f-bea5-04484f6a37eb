import React from "react";

export default function AgentsLoading() {
  return (
    <div className="bg-white relative">
      {/* Hero Section Skeleton */}
      <div className="relative bg-gradient-to-r from-blue-50 to-purple-50 py-20">
        <div className="container mx-auto px-4 text-center">
          <div className="animate-pulse">
            <div className="h-12 bg-gray-300 rounded-lg mx-auto mb-6 max-w-2xl"></div>
            <div className="h-6 bg-gray-200 rounded-lg mx-auto mb-8 max-w-xl"></div>
            <div className="h-12 bg-gray-300 rounded-lg mx-auto max-w-md"></div>
          </div>
        </div>
      </div>

      {/* Search and Filter Section Skeleton */}
      <div className="py-8">
        <div className="container mx-auto px-4">
          <div className="animate-pulse">
            <div className="h-12 bg-gray-200 rounded-lg mb-6 max-w-md mx-auto"></div>
            <div className="flex justify-center space-x-4 mb-8">
              {[...Array(6)].map((_, i) => (
                <div
                  key={i}
                  className="h-10 bg-gray-200 rounded-full w-24"
                ></div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Agents Grid Skeleton */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 max-w-7xl mx-auto">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
                    <div className="h-4 bg-gray-200 rounded w-24"></div>
                  </div>
                  <div className="h-6 bg-gray-300 rounded mb-3"></div>
                  <div className="space-y-2 mb-4">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                  <div className="h-10 bg-gray-200 rounded w-24"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
