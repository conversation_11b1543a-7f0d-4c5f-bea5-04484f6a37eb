"use client";

import React, { useEffect, useState } from "react";
import <PERSON><PERSON><PERSON> from "./agents-hero";
import AgentsT<PERSON> from "./agents-top";
import HorizontalCarousel from "./agents-carousel";
import AgentsContent from "./agents-content";
import AgentsContact from "./agents-contact";
import Image from "next/image";
import { Agent } from "../page";

export interface AgentsPageClientProps {
  initialAgents: Agent[];
}

// Transform backend agent data to match the existing component interface
interface LegacyAgentItem {
  id: string;
  section: string;
  title: string;
  excerpt: string;
  bot: string;
  color: string;
}

const transformAgentData = (agents: Agent[]): LegacyAgentItem[] => {
  return agents.map((agent) => ({
    id: agent.id,
    section: agent.category || "General",
    title: agent.app_name,
    excerpt: agent.app_description,
    bot: agent.app_logo || "/images/bot-gray.svg", // fallback image
    color: getCategoryColor(agent.category),
  }));
};

const getCategoryColor = (category: string): string => {
  const colorMap: { [key: string]: string } = {
    "Application Performance Monitoring": "#1D5A69",
    "Cloud Monitoring": "#344054",
    "Website Testing": "#700902",
    "Webhook Testing": "#2E8DFF",
    "Social Media": "#009143",
    Agent: "#7141F8",
    "": "#344054", // default color
  };
  return colorMap[category] || "#344054";
};

export default function AgentsPageClient({
  initialAgents,
}: AgentsPageClientProps) {
  const [agentsSearch, setAgentsSearch] = useState("");
  const [agentsCategory, setAgentsCategory] = useState("All");
  const [agentsDisplayed, setAgentsDisplayed] = useState<LegacyAgentItem[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [allAgents, setAllAgents] = useState<LegacyAgentItem[]>([]);
  const availableCategories = [
    "All",
    "Customer Support",
    "Sales and Marketing",
    "Engineering DevOps",
    "Documents and Analysis",
    "Strategy Business",
    "Content and Video",
  ];

  const [form, setForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    monitoringNeed: "",
  });

  const isFormValid =
    form.firstName && form.lastName && form.email && form.monitoringNeed;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const handleAgentsSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setAgentsSearch("");
  };

  const handleAgentsChange = () => {
    let filteredAgents = [...allAgents];

    // Filter by category
    if (agentsCategory !== "All") {
      filteredAgents = filteredAgents.filter(
        (item) => item.section === agentsCategory
      );
    }

    // Filter by search term
    if (agentsSearch !== "") {
      filteredAgents = filteredAgents.filter((item) =>
        item.title.toLowerCase().includes(agentsSearch.toLowerCase())
      );
    }

    setAgentsDisplayed(filteredAgents);
  };

  // Initialize agents data
  useEffect(() => {
    const transformedAgents = transformAgentData(initialAgents);
    setAllAgents(transformedAgents);
    setAgentsDisplayed(transformedAgents);
  }, [initialAgents]);

  // Handle filtering when search or category changes
  useEffect(() => {
    handleAgentsChange();
  }, [agentsCategory, agentsSearch, allAgents]);

  return (
    <>
      <AgentsHero
        agentsSearch={agentsSearch}
        handleAgentsSearch={handleAgentsSearch}
        setAgentsSearch={setAgentsSearch}
      />
      <div className="space-y-5">
        <AgentsTop
          agentsSearch={agentsSearch}
          handleAgentsSearch={handleAgentsSearch}
          setAgentsSearch={setAgentsSearch}
          initialAgents={initialAgents}
        />
        <HorizontalCarousel
          setAgentsCategory={setAgentsCategory}
          categories={availableCategories}
        />
      </div>
      <AgentsContent
        agentsDisplayed={agentsDisplayed}
        setOpenDialog={setOpenDialog}
      />
      <AgentsContact />
      {openDialog && (
        <div className="fixed top-0 right-0 left-0 bottom-0 flex items-center backdrop-blur-sm justify-center max-h-screen z-50">
          <div className="w-[90%] md:w-[60%] md:h-[70%]">
            <div className="flex border border-blue-800 rounded-lg w-full h-full bg-white ">
              <div className="rounded-lg hidden md:flex w-2/5">
                <Image
                  src={"/images/agents-request-image.svg"}
                  height={607}
                  width={422}
                  className="rounded-lg object-cover w-full h-full"
                  alt="Request Agent Image"
                />
              </div>
              <div className="rounded-lg w-full md:w-3/5">
                <form action="" className="p-5 space-y-6">
                  <div className="">
                    <h2 className="font-bold text-xl">Request an Agent</h2>
                    <p className="text-sm text-[#475467]">
                      {`Can't find an agent you need? Feel free to shoot us a
                      request and we will try to bring them on.`}
                    </p>
                  </div>
                  <div className="flex gap-4">
                    <div className="flex flex-col text-sm w-1/2 gap-2">
                      <label htmlFor="firstName">First Name</label>
                      <input
                        type="text"
                        placeholder="John"
                        className="text-[#475467] border outline-none rounded-md py-3 px-4 w-full"
                        onChange={handleChange}
                        value={form.firstName}
                        name="firstName"
                      />
                    </div>
                    <div className="flex flex-col text-sm w-1/2 gap-2">
                      <label htmlFor="lastName">Last Name</label>
                      <input
                        type="text"
                        placeholder="Doe"
                        className="text-[#475467] border outline-none rounded-md py-3 px-4 w-full"
                        onChange={handleChange}
                        value={form.lastName}
                        name="lastName"
                      />
                    </div>
                  </div>
                  <div className="flex flex-col text-sm gap-2">
                    <label htmlFor="email">Email</label>
                    <input
                      type="email"
                      placeholder="<EMAIL>"
                      className="text-[#475467] border outline-none rounded-md py-3 px-4"
                      onChange={handleChange}
                      value={form.email}
                      name="email"
                    />
                  </div>
                  <div className="flex flex-col text-sm gap-2">
                    <label htmlFor="monitoringNeed">Monitoring Need</label>
                    <input
                      type="text"
                      placeholder="E.g. I need an agent to monitor API response times"
                      maxLength={80}
                      max={80}
                      className=" border outline-none rounded-md py-3 px-4"
                      onChange={handleChange}
                      value={form.monitoringNeed}
                      name="monitoringNeed"
                    />
                  </div>
                  <div className="flex items-center gap-4 justify-end text-sm">
                    <button
                      onClick={() => setOpenDialog(false)}
                      className="border px-4 py-3 rounded-md"
                    >
                      Cancel
                    </button>
                    <button
                      disabled={!isFormValid}
                      className={`px-4 py-3 border rounded-md ${!isFormValid ? "opacity-60 cursor-not-allowed" : "opacity-100"}`}
                    >
                      Send Request
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
