import React from "react";
import { fetchAgents } from "../lib/api";

export default async function DebugAgents() {
  const agents = await fetchAgents();

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Debug Agents</h1>
      <p className="mb-4">Total agents: {agents.length}</p>
      
      <div className="space-y-4">
        {agents.map((agent, index) => (
          <div key={index} className="border p-4 rounded">
            <h3 className="font-bold">ID: {agent.id}</h3>
            <p>Name: {agent.app_name}</p>
            <p>Category: {agent.category}</p>
            <p>Description: {agent.app_description}</p>
            <p>Active: {agent.is_active ? "Yes" : "No"}</p>
            <p>Logo: {agent.app_logo}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
