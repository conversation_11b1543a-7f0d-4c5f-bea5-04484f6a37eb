import { Agent } from "../page";

const API_BASE_URL = "https://api.staging.telex.im/api/v1";

/**
 * Fetches all agents from the API
 * @returns Promise<Agent[]> - Array of agents or empty array on error
 */
export async function fetchAgents(): Promise<Agent[]> {
  try {
    const response = await fetch(`${API_BASE_URL}/agents`, {
      cache: "no-store", // Ensure fresh data on each request
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      console.error(
        "Failed to fetch agents:",
        response.status,
        response.statusText
      );
      return [];
    }

    const data = await response.json();

    if (data.status !== "success") {
      console.error("API returned error status:", data.message);
      return [];
    }

    return data.data || [];
  } catch (error) {
    console.error("Error fetching agents:", error);
    return [];
  }
}

/**
 * Fetches a single agent by ID
 * @param id - Agent ID
 * @returns Promise<Agent | null> - Agent object or null if not found
 */
export async function fetchAgentById(id: string): Promise<Agent | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/agents/${id}`, {
      cache: "no-store",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      console.error(
        "Failed to fetch agent:",
        response.status,
        response.statusText
      );
      return null;
    }

    const data = await response.json();
    return data.data || null;
  } catch (error) {
    console.error("Error fetching agent:", error);
    return null;
  }
}
