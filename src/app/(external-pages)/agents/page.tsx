import React from "react";
import { Metada<PERSON> } from "next";
import { fetchAgents } from "./lib/api";
import AgentsPageClient from "./components/agents-page-client";

// Metadata for SEO
export const metadata: Metadata = {
  title: "AI Agents Marketplace | Telex",
  description:
    "Discover and integrate powerful AI agents for monitoring, automation, and business optimization. Browse our marketplace of specialized agents for various industries.",
  keywords:
    "AI agents, automation, monitoring, business optimization, marketplace, integrations",
  openGraph: {
    title: "AI Agents Marketplace | Telex",
    description:
      "Discover and integrate powerful AI agents for monitoring, automation, and business optimization.",
    type: "website",
  },
};

// Types for the agent API response
interface AgentCapabilities {
  streaming: boolean;
  pushNotifications: boolean;
  stateTransitionHistory: boolean;
}

interface AgentProvider {
  organization: string;
  url: string;
}

export interface Agent {
  id: string;
  app_name: string;
  json_url: string;
  app_url: string;
  app_logo: string;
  owner_id: string;
  app_description: string;
  info: string;
  is_active: boolean;
  category: string;
  status: string;
  is_paid: boolean;
  is_approved: boolean;
  prices: any;
  version: string;
  provider: AgentProvider;
  default_input_modes: any;
  default_output_modes: any;
  preshared_key: string;
  created_at: string;
  updated_at: string;
  skills: any[];
  is_system: boolean;
  commission_rate: number;
  capabilities: AgentCapabilities;
  linked: boolean;
}

export default async function Agents() {
  const agentsData = await fetchAgents();

  return (
    <div className="bg-white relative">
      <AgentsPageClient initialAgents={agentsData} />
    </div>
  );
}
