"use client";

import React from "react";
import { Button } from "~/components/ui/button";
import Image from "next/image";
import { Settings } from "lucide-react";
import Link from "next/link";
import Features from "~/telexComponents/externalFeatures";
import Faq from "~/telexComponents/homePageFaqs";
import TestimonialList from "~/telexComponents/testimonials";
import HowItWorks from "~/telexComponents/HowItWorks";
import AgentsWrap from "~/telexComponents/agentsWrap";
import AutopilotCTA from "~/telexComponents/autopilotCTA";

//

export default function Home() {
  return (
    <main>
      <header className="">
        <div className="bg-[#FFFFFF] px-6 pt-8 md:pt-16 relative overflow-hidden">
          <Image
            src="/left-sun-ray.png"
            alt="sun ray decor"
            width={400}
            height={351}
            className="absolute top-0 hidden sm:block left-0 z-0 pointer-events-none"
          />

          <Image
            src="/left-sun-ray.png"
            alt="sun ray decor"
            width={400}
            height={351}
            className="absolute top-0 left-1/2 hidden md:block mr-4 z-0 pointer-events-none"
          />

          <Image
            src="/right-sun-ray.png"
            alt="sun ray decor"
            width={400}
            height={351}
            className="absolute top-0 right-1/2 hidden md:block ml-4 z-0 pointer-events-none"
          />

          <Image
            src="/right-sun-ray.png"
            alt="sun ray decor"
            width={400}
            height={351}
            className="absolute top-0 hidden sm:block right-0 z-0 pointer-events-none"
          />

          <div className="max-w-5xl mx-auto space-y-5 mt-6">
            <h1 className="lg:text-5xl md:text-4xl text-3xl font-semibold text-center mx-auto leading-normal md:leading-snug lg:leading-snug">
              The workspace for Al{" "}
              <span className="text-primary-500 ">
                {" "}
                and Humans to <br />
                collaborate
              </span>
            </h1>

            <p className="text-muted-foreground text-center max-w-3xl mx-auto lg:text-lg">
              Build your own Al colleagues, assign them work in channels, and
              watch them grow your business.
            </p>

            <div className="flex justify-center">
              <Link href="/auth/sign-up">
                <Button className="bg-gradient-to-b from-[#8860F8] to-[#7141F8] hover:bg-opacity-80 text-white font-medium px-8 py-6">
                  Get Started
                </Button>
              </Link>

              {/* <Link href="/download">
                <Button className="bg-white hover:bg-gray-100 cursor-pointer border border-primary-500 text-primary-500 font-medium px-8 py-6">
                  Get Telex mobile
                </Button>
              </Link> */}
            </div>
          </div>

          <div className="relative max-w-6xl mx-auto w-full lg:mt-16 md:mt-20 mt-6 px-6 md:px-0">
            <Image
              src="/left-spiral.svg"
              width={255}
              height={600}
              className="hidden sm:block absolute md:-left-32 md:-top-36 -top-20 -left-20 z-0"
              alt="telex"
            />
            <div className="mx-auto w-fit z-10 relative">
              <Image
                src="/new-hero-img.png"
                width={1000}
                height={1000}
                className="xl:h-[475px] lg:h-[300px] md:h-[300px] w-auto"
                alt="telex"
              />
            </div>
            <Image
              src="/right-spiral.svg"
              width={500}
              height={600}
              className="hidden sm:block absolute md:-right-64 md:-top-36 -top-20 w-[400px] -right-28 z-0"
              alt="telex"
            />
          </div>
        </div>
      </header>

      <section className="lg:py-24 py-12">
        <div className="flex justify-center">
          <div className="px-3 w-fit py-2 text-sm mb-4 font-medium text-[#40258D] border-2 border-[#F1F1FE] rounded-[50px] bg-white flex items-center gap-1">
            <Settings className="h-4 w-4" />
            <span>How it works</span>
          </div>
        </div>

        <div className="text-center mt-4 mb-10 max-w-3xl mx-auto w-full px-6">
          <h2 className="text-[#101828] font-semibold sm:text-4xl text-3xl leading-10">
            Let humans do the important stuff, and let your Al co-workers do the
            rest
          </h2>
          <p className="text-[#344054] text-base font-normal leading-6 mt-2">
            {" "}
            We have 1000s of Al specialists who can do almost anything your
            business needs and if you can't find what you need - simply build
            your own!
          </p>
        </div>

        <HowItWorks />
      </section>

      <Features />

      <AgentsWrap />

      <div className="w-full px-6">
        <Faq />
      </div>
      <TestimonialList />
      <AutopilotCTA />
    </main>
  );
}
