"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Site Security - Telex",
    description:
      "Protect your digital assets with advanced security measures. Monitor threats, prevent attacks, and ensure your website's security with intelligent protection.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/site-security-case-1.png";
import CaseTwo from "../_assets/site-security-case-2.png";
import { technologies } from "../data/technologies";

const firstData = [
  {
    id: 1,
    title: "Use Telex for Security Policy Maintenance",
    content: `Detect violations of your own security policies like public S3 buckets, disabled firewalls, or open ports before they put you at risk.`,
  },
  {
    id: 2,
    title: "Downtime & Uptime Tracking with Telex",
    content: `Monitor site availability around the clock. If your site goes down or responds unusually slow, Telex will let you know immediately.`,
  },
  {
    id: 3,
    title: "Threat Monitoring with Telex",
    content: `Automatically track suspicious activity across your site and servers. Get alerted the moment something looks off, no manual checks required.`,
  },
  {
    id: 4,
    title: "Use Telex for Vulnerability Detection",
    content: `Continuously scan your system for security gaps like outdated software or weak configurations, so you can fix issues before attackers find them.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex Security prevented a major data breach",
    image: CaseOne?.src,
    content: `An e-commerce platform strengthened their security posture with Telex, preventing potential data breaches and maintaining customer trust.`,
  },
  {
    id: 2,
    title: "DDoS protection success story",
    image: CaseTwo.src,
    content: `Learn how a financial services company maintained 99.99% uptime during multiple DDoS attacks using our security system.`,
  },
  {
    id: 3,
    title: "Transforming website security",
    image: CaseOne.src,
    content: `Discover how a healthcare provider achieved HIPAA compliance and protected sensitive patient data with our security solutions.`,
  },
  {
    id: 4,
    title: "Enterprise-scale security implementation",
    image: CaseTwo?.src,
    content: `See how a global retail chain secured their multi-regional websites and protected millions of customer transactions.`,
  },
];

const siteSecurityFaq = [
  {
    id: 1,
    question: "What types of security threats can Telex detect?",
    answer:
      "Telex can detect a wide range of security threats including DDoS attacks, SQL injections, malware infections, unauthorized access attempts, data breaches, and suspicious network activity. Our advanced threat detection system uses AI and machine learning to identify both known and emerging security threats in real-time.",
  },
  {
    id: 2,
    question: "How does Telex monitor my site and infrastructure?",
    answer:
      "Telex employs a comprehensive monitoring system that includes real-time traffic analysis, continuous vulnerability scanning, and 24/7 infrastructure surveillance. We use distributed monitoring nodes to track your site's security status, performance metrics, and potential threats across all layers of your technology stack.",
  },
  {
    id: 3,
    question: "Is Telex easy to integrate with my existing security tools?",
    answer:
      "Yes, Telex is designed to seamlessly integrate with most popular security tools and platforms. We provide robust APIs and pre-built integrations for common security solutions, firewalls, and SIEM systems. Our technical team can assist with custom integrations to ensure Telex works harmoniously with your existing security infrastructure.",
  },
  {
    id: 4,
    question: "Does Telex provide guidance on how to fix security issues?",
    answer:
      "Yes, Telex provides detailed remediation guidance for all identified security issues. Our platform includes step-by-step instructions, best practices, and actionable recommendations to help you address vulnerabilities. We also offer access to security experts who can provide additional support when needed.",
  },
];

const SiteSecurity = () => {
  return (
    <>
      <Hero
        breadCumbs="Site Security"
        title="Smarter {{Site Security for Safer Operations}}"
        content="From suspicious activity to critical vulnerabilities, Telex flags risks and helps you fix them before they become problems."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="Built to Keep You Secure at Every Layer"
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`Organizations trust Telex security solutions to protect their digital assets and maintain business continuity. See how our comprehensive security measures can safeguard your online presence.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Complete security coverage across all platforms"
        items={technologies}
      />
      {/* <Guides /> */}
      <Faq faq={siteSecurityFaq} />
      <KeepTrack
        title="Protect Your Infrastructure with Confidence"
        content="Deploy Al-powered protection that adapts to your environment and works for you."
      />
    </>
  );
};

export default SiteSecurity;
