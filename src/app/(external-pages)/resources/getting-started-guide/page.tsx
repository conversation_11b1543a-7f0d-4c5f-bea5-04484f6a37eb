"use client";

import { ChevronRight, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import CTASection from "../_components/cta_section";

const sections = [
  { id: "account-creators", label: "For Account Creators" },
  { id: "invited-users", label: "For Invited Users" },
];

const GettingStartedGuide = () => {
  const [activeSection, setActiveSection] = useState("account-creators");
  const [openSidebar, setOpenSidebar] = useState(false);
  const sectionRefs = useRef<Record<string, HTMLElement | null>>({});

  useEffect(() => {
    const topOffset = 112;

    const handleScroll = () => {
      let closestSectionId = sections[0].id;
      let closestDistance = Infinity;

      for (const section of sections) {
        const el = sectionRefs.current[section.id];
        if (el) {
          const distance = Math.abs(el.getBoundingClientRect().top - topOffset);
          if (distance < closestDistance) {
            closestDistance = distance;
            closestSectionId = section.id;
          }
        }
      }

      setActiveSection(closestSectionId);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToSection = (id: string) => {
    const el = sectionRefs.current[id];
    if (el) {
      const topOffset = 112;
      const elementTop = el.getBoundingClientRect().top + window.scrollY;
      const scrollTo = elementTop - topOffset;

      window.scrollTo({ top: scrollTo, behavior: "smooth" });
    }
  };

  return (
    <main className="max-w-[1400px] mx-auto pt-8">
      <div className="flex">
        <div
          className={`xl:hidden ${openSidebar ? "hidden" : "flex items-center justify-center"} sticky top-20 left-0 h-screen border-r border-[#F2F4F7] px-1`}
          onClick={() => setOpenSidebar(true)}
        >
          <ChevronRight size={20} />
        </div>
        <div
          className={`xl:block ${openSidebar ? "block" : "hidden"} fixed z-20 xl:z-0 xl:sticky bg-white top-20 h-screen left-0 pl-10 xl:pl-[100px] py-20 pr-10 shadow-sm border-x border-[#F2F4F7]`}
        >
          <div className="w-[100px] md:w-44 space-y-5">
            <div className="flex flex-col md:flex-row items-center text-center md:text-left gap-[14px] text-sm text-[#344054] font-medium">
              <div className="relative w-12 h-12 rounded-xl overflow-hidden bg-[#E6F1FF] flex items-center justify-center">
                <Image
                  src="/images/bot_dark_blue.svg"
                  alt="Telex Guide"
                  width={32}
                  height={32}
                />
              </div>
              <h3>Telex Team</h3>
            </div>
            <div className="text-[#101828] text-sm font-normal space-y-4">
              {sections.map((section) => (
                <h3
                  key={section.id}
                  onClick={() => scrollToSection(section.id)}
                  className={`transition-all duration-300 cursor-pointer ${
                    activeSection === section.id
                      ? "text-[#7141F8]"
                      : "hover:text-[#7141F8]"
                  }`}
                >
                  {section.label}
                </h3>
              ))}
            </div>
          </div>
          <X
            size={16}
            className="absolute top-4 right-4 block xl:hidden"
            onClick={() => setOpenSidebar(false)}
          />
        </div>

        <div className="w-full">
          {/* Breadcrumb */}
          <div className="bg-[#F9FAFB] flex justify-center md:justify-start items-center gap-3 px-4 xl:px-10 py-5 text-[#667085] text-[10px] md:text-sm font-normal">
            <Link
              href="/"
              className="transition-all duration-300 hover:underline"
            >
              Home
            </Link>
            <ChevronRight width={9} height={21} />
            <Link
              href="/resources"
              className="transition-all duration-300 hover:underline"
            >
              Resources
            </Link>
            <ChevronRight width={9} height={21} />
            <h3 className="text-[#1D2939]">Getting Started with Telex</h3>
          </div>

          {/* Guide content */}
          <div className="flex flex-col items-center md:items-start px-4 xl:px-10 py-[30px] border-y border-[#F2F4F7] space-y-8">
            <div className="space-y-4">
              <div className="flex items-center justify-center md:justify-start gap-3 text-[10px] md:text-sm text-[#667085]">
                <h5 className="text-[#E36914] font-medium">ESSENTIAL GUIDE</h5>
                <div className="bg-[#D0D5DD] w-1.5 h-1.5 rounded-full" />
                <h5>5 min read</h5>
                <div className="bg-[#D0D5DD] w-1.5 h-1.5 rounded-full" />
                <h5>Last updated: 29 March 2025</h5>
              </div>
              <h1 className="text-[#101828] text-center md:text-left text-xl md:leading-9 md:text-[42px] font-semibold">
                Getting Started with Telex
              </h1>
              <p className="text-[#344054] text-center md:text-left text-sm md:text-lg font-normal">
                Whether you're creating a Telex account for yourself or joining
                a team, this quick guide will help you hit the ground running.
              </p>
            </div>
            <div className="w-[310px] h-full md:w-[600px] xl:w-[995px] md:h-[222px] flex items-center justify-center border border-[#F2F4F7] rounded-lg shadow-md bg-gradient-to-r from-[#FEF1E8] to-[#E6F1FF] p-4 xl:p-0">
              <div className="flex flex-col md:flex-row gap-[20px] items-center">
                <div className="flex items-center gap-4">
                  <div className="bg-[#E6F1FF] w-20 h-20 flex items-center justify-center border-2 border-white rounded-2xl">
                    <Image
                      src="/images/bot_dark_blue.svg"
                      alt="Telex Bot"
                      width={40}
                      height={40}
                    />
                  </div>
                  <div className="text-center md:text-left">
                    <h2 className="text-[#101828] text-lg md:text-xl font-semibold">
                      Welcome to Telex!
                    </h2>
                    <p className="text-[#667085] text-sm">
                      Let's get you started in just a few steps
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="pt-12 pb-20 px-4 xl:px-10 space-y-16 w-[320px] md:w-[600px] xl:w-[995px]">
            {/* For Account Creators */}
            <div
              id="account-creators"
              ref={(el) => {
                sectionRefs.current["account-creators"] = el;
              }}
              className="space-y-8"
            >
              <h1 className="text-[#101828] text-base md:text-2xl font-semibold">
                For Account Creators (Solo or Team Use)
              </h1>
              <p className="text-[#344054] text-sm md:text-base font-normal">
                If you're the one setting up a Telex account, either for
                yourself or for a team, here's how to get started:
              </p>

              <div className="space-y-8">
                {/* Step 1 */}
                <div className="space-y-4">
                  <h2 className="text-[#101828] text-base md:text-xl font-semibold flex items-center gap-3">
                    <span className="bg-[#E36914] text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold">
                      1
                    </span>
                    Choose a Pricing Plan
                  </h2>
                  <p className="text-[#344054] text-sm md:text-base font-normal ml-9">
                    Head to the Pricing page and select the plan that best fits
                    your needs.
                  </p>
                </div>

                {/* Step 2 */}
                <div className="space-y-4">
                  <h2 className="text-[#101828] text-base md:text-xl font-semibold flex items-center gap-3">
                    <span className="bg-[#E36914] text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold">
                      2
                    </span>
                    Sign Up Using Your Email or Google Account
                  </h2>
                  <p className="text-[#344054] text-sm md:text-base font-normal ml-9">
                    Once you've selected a plan, you'll be prompted to sign up
                    using your email or Google account.
                  </p>
                </div>

                {/* Step 3 */}
                <div className="space-y-4">
                  <h2 className="text-[#101828] text-base md:text-xl font-semibold flex items-center gap-3">
                    <span className="bg-[#E36914] text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold">
                      3
                    </span>
                    Set Up Your Organization Info
                  </h2>
                  <p className="text-[#344054] text-sm md:text-base font-normal ml-9">
                    Provide a few basic details:
                  </p>
                  <ul className="text-[#344054] text-sm md:text-base font-normal ml-9 space-y-2">
                    <li>• Organization name</li>
                    <li>• Organization email</li>
                    <li>• Organization type</li>
                    <li>• Country</li>
                  </ul>
                </div>

                {/* Step 4 */}
                <div className="space-y-4">
                  <h2 className="text-[#101828] text-base md:text-xl font-semibold flex items-center gap-3">
                    <span className="bg-[#E36914] text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold">
                      4
                    </span>
                    Invite Team Members
                  </h2>
                  <div className="ml-9 space-y-3">
                    <p className="text-[#344054] text-sm md:text-base font-normal">
                      After setup, you'll land on your Telex dashboard.
                    </p>
                    <ul className="text-[#344054] text-sm md:text-base font-normal space-y-2">
                      <li>• You'll be prompted to invite members right away</li>
                      <li>
                        • You can always invite later by going to Organization
                        Settings (click your avatar → Organization Settings →
                        Invitations)
                      </li>
                      <li>• Enter emails to send invite links</li>
                    </ul>
                  </div>
                </div>

                {/* Step 5 */}
                <div className="space-y-4">
                  <h2 className="text-[#101828] text-base md:text-xl font-semibold flex items-center gap-3">
                    <span className="bg-[#E36914] text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold">
                      5
                    </span>
                    Explore the Agent Marketplace
                  </h2>
                  <div className="ml-9 space-y-3">
                    <ul className="text-[#344054] text-sm md:text-base font-normal space-y-2">
                      <li>• Click the Agent icon on the sidebar</li>
                      <li>
                        • You'll see a blank list if you haven't activated any
                        agents yet
                      </li>
                      <li>• Click "Browse Agents" to open the marketplace</li>
                      <li>• Browse by category or search for what you need</li>
                      <li>
                        • Click on an agent to view its details, then toggle the
                        switch to activate
                      </li>
                      <li>
                        • Once activated, the agent will appear in your Active
                        Agents list and can start sending alerts, reports, or
                        summaries to you or a channel
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* For Invited Users */}
            <div
              id="invited-users"
              ref={(el) => {
                sectionRefs.current["invited-users"] = el;
              }}
              className="space-y-8"
            >
              <h1 className="text-[#101828] text-base md:text-2xl font-semibold">
                For Invited Users
              </h1>
              <p className="text-[#344054] text-sm md:text-base font-normal">
                If you've received an invitation to join a Telex organization,
                here's what to do:
              </p>

              <div className="space-y-8">
                {/* Step 1 */}
                <div className="space-y-4">
                  <h2 className="text-[#101828] text-base md:text-xl font-semibold flex items-center gap-3">
                    <span className="bg-[#2E8DFF] text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold">
                      1
                    </span>
                    Open Your Invite Link
                  </h2>
                  <p className="text-[#344054] text-sm md:text-base font-normal ml-9">
                    Click the link sent to your email. It will take you to the
                    Telex sign-up page.
                  </p>
                </div>

                {/* Step 2 */}
                <div className="space-y-4">
                  <h2 className="text-[#101828] text-base md:text-xl font-semibold flex items-center gap-3">
                    <span className="bg-[#2E8DFF] text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold">
                      2
                    </span>
                    Sign Up or Log In
                  </h2>
                  <div className="ml-9 space-y-2">
                    <ul className="text-[#344054] text-sm md:text-base font-normal space-y-2">
                      <li>
                        • If you don't have a Telex account yet, sign up using
                        your email or Google account.
                      </li>
                      <li>• If you already have an account, just log in.</li>
                    </ul>
                  </div>
                </div>

                {/* Step 3 */}
                <div className="space-y-4">
                  <h2 className="text-[#101828] text-base md:text-xl font-semibold flex items-center gap-3">
                    <span className="bg-[#2E8DFF] text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold">
                      3
                    </span>
                    You're In!
                  </h2>
                  <p className="text-[#344054] text-sm md:text-base font-normal ml-9">
                    After authentication, you'll be taken directly into the
                    organization that invited you. You'll see the dashboard and
                    any active agents or channels already set up by the admin.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CTASection />
    </main>
  );
};

export default GettingStartedGuide;
