"use server";
import React from "react";
import { <PERSON>ada<PERSON> } from "next";
import <PERSON> from "next/link";
import { <PERSON><PERSON> } from "~/components/ui/button";
import Image from "next/image";
import WorkflowsHeroImage from '../_assets/workflow/workspace-hero.png'
import HowWOrkflowWork from '../_assets/workflow/how-workflows-work.png'
import Brain from "../_assets/workflow/Brain.png"
import FilmSlate from "../_assets/workflow/FilmSlate.png"
import Lightning from "../_assets/workflow/Lightning.png"
import Newspaper from "../_assets/workflow/Newspaper.png"
import Robot from "../_assets/workflow/Robot.png"
import TreeStructure from "../_assets/workflow/TreeStructure.png"
import TrendUp from "../_assets/workflow/TrendUp.png"
import { ArrowRight } from "lucide-react";
import { color } from "framer-motion";

export const generateMetadata = async (): Promise<Metadata> => {
  return {
    title: "Workflows - Telex",
    description:
      "Automate your business processes with intelligent workflow solutions. Streamline operations, reduce manual tasks, and improve efficiency with AI-powered automation.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};

const WorkflowsPage = () => {
  const workflowFeatures = [
    {
      id: 1,
      title: "Automated Process Management",
      content:
        "Design and deploy intelligent workflows that automate repetitive tasks, reduce manual errors, and accelerate business processes across your organization.",
    },
    {
      id: 2,
      title: "CI/CD Pipeline Integration",
      content:
        "Seamlessly integrate with your development workflows. Monitor deployments, automate testing, and ensure smooth continuous integration and delivery.",
    },
    {
      id: 3,
      title: "Real-time Workflow Monitoring",
      content:
        "Track workflow performance in real-time with comprehensive analytics. Get instant alerts when processes fail or require attention.",
    },
    {
      id: 4,
      title: "Custom Workflow Builder",
      content:
        "Create custom workflows tailored to your business needs using our intuitive drag-and-drop interface. No coding required.",
    },
  ];

  const  workflowSolutions = [
    {
      id: 1,
      title: "DevOps",
      content:
        "Catch system failures, slowdowns, and outages before they impact your customers.",
      icon: FilmSlate,
      color:"#030506"
    },
    {
      id: 2,
      title: "Marketing",
      content:
        "Plan campaigns, write content, and stay consistent across every channel.",
      icon: TrendUp,
      color:"#00CC5F"
    },
    {
      id: 3,
      title: "Video & Media",
      content:
        "Clone viral videos, create scripts, and post across platforms faster than ever.",
      icon: FilmSlate,
      color: "#8686F9"
    },
    {
      id: 4,
      title: "Customer Support",
      content:
        "Handle tickets, reply to emails, and resolve issues automatically and on time.",
      icon: Robot,
      color: "#2E8DFF"
    },
    {
      id: 5,
      title: "Finance",
      content:
        "Process invoices, review documents, and handle approvals with AI accuracy.",
      icon: Brain,
      color: "#FA8F45"
    },
    {
      id: 6,
      title: "Data",
      content:
        "Extract insights, summarize reports, and analyze trends with zero spreadsheets.",
      icon: TreeStructure,
      color: "#1A5260"
    },
    {
      id: 7,
      title: "Productivity & Ops",
      content:
        "Automate routine tasks, internal requests, and recurring workflows in one click.",
      icon: Lightning,
      color: "#68909B"
    },
    {
      id: 8,
      title: "Sales",
      content:
        "Find leads, personalize outreach, and follow up without lifting a finger.",
      icon: Newspaper,
      color: "#B00E03"
    },
    {
      id: 9,
      title: "Content",
      content:
        "Write blogs, turn ideas into articles, and repurpose existing content in minutes.",
      icon: Robot,
      color: "#2C2C68"
    },

  ];

  const workflowSteps = [
    {
      step: "01",
      title: "Design Your Workflow",
      description:
        "Use our intuitive workflow builder to design automated processes that fit your business needs. Define triggers, actions, and conditions with ease.",
    },
    {
      step: "02",
      title: "Connect Your Tools",
      description:
        "Integrate with your existing tools and platforms. Connect APIs, databases, and third-party services to create comprehensive automation.",
    },
    {
      step: "03",
      title: "Monitor & Optimize",
      description:
        "Track workflow performance with real-time analytics. Receive alerts for failures and continuously optimize your processes for better efficiency.",
    },
  ];

  return (
    <div className="w-screen flex items-center justify-center flex-col">
      {/* Hero Section */}
      <div className="flex flex-col items-center justify-center text-center py-20 md:py-[120px] bg-white px-2 md:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto space-y-5 mt-6">
          <h1 className="lg:text-5xl md:text-4xl text-3xl font-semibold text-center mx-auto leading-normal md:leading-snug lg:leading-snug">
            <span className="text-primary-500 ">
              {" "}
              Powerful Workflows
            </span>{" "}
            That Do the Heavy <br/> Lifting for You
          </h1>

          <p className="text-muted-foreground text-center max-w-3xl mx-auto lg:text-lg">
            Telex workflows combine multiple AI agents to complete complex tasks, so you can move faster without switching tools.
          </p>
          <div className="!mt-12">
            <Image className="w-full h-auto object-cover" width={2000} height={2000} alt="workflows hero" src={WorkflowsHeroImage}/>
          </div>

        </div>
      </div>

      {/* Features Section */}
      <div className="bg-gray-50 flex items-center justify-center w-full py-[100px]">
          <div className="max-w-7xl grid grid-cols-1 md:grid-cols-2 gap-12 justify-between">
            <div className="pr-10">
              <p className="lg:text-3xl md:text-2xl text-2xl font-semibold text-left leading-normal md:leading-snug lg:leading-snug">
                How
                <span className="text-primary-500 ">
                  {" "}
                   Workflows
                </span>{" "}
                in <br/> Telex Works
              </p>
              <p className="mb-1 text-[#344054] text-base max-w-[532px] mt-5">
              Telex workflows are simple, prebuilt, multi-step automations powered by AI agents.
              </p>
              <p className="">Each agent joins a  chat-like thread, picks up its task, and hands off to the next just like real coworkers would collaborating in real time.</p>

              <p className="mt-10">You don’t need to design flows.</p>
              <p className="my-1">You don’t need to code anything.</p>
              <p>Just pick a workflow and go.</p>

              <Link href="/agents">
                <Button className="bg-gradient-to-b from-[#8860F8] to-[#7141F8] hover:bg-opacity-80 text-white font-medium p-6 glex gap-4 mt-12">
                  Start Using a Workflow
                </Button>
              </Link>
            </div>
            <div className="bg-[#FEF1E8] px-16 py-10 flex items-center justify-center">
                <div className="bg-white p-12 rounded-2xl">
                  <Image src={HowWOrkflowWork} className="w-full h-auto object-cover" alt="how workflows work" width={2400} height={2400} quality={100}/>
                </div>
            </div>
        </div>
      </div>

      {/* Workflow Solutions Section */}
      <div className="px-4 md:px-6 py-[100px] bg-white">
        <div className="max-w-7xl mx-auto">

          <div className="w-full flex justify-center mb-10">
            <div className="w-fit flex items-center justify-between rounded-[50px] text-[#40258D] gap-1 bg-gradient-to-b from-[#FFFFFF] via-[#FFFFFF] via-[25.85%] to-[#F2EFFA] border-2 border-[#F1F1FE] px-3 py-2">
              <Image
                src="/images/StarFour.svg"
                alt="Robot Image"
                height={16}
                width={16}
              />
              <h3>Categories</h3>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          
            {workflowSolutions.map((solution) => (
              <div
                key={solution.id}
                className={`py-6 bg-white rounded-lg border border-gray-200 hover:border-primary-300 transition-colors duration-200 p-6 cursor-pointer`} >
                <div className="relative w-fit h-fit mb-2">
                  <div style={{ backgroundColor: solution.color }} className="absolute inset-0 rounded-xl opacity-30" />
                  <div className="relative w-fit h-fit p-1 rounded-xl">
                    <div style={{ backgroundColor: solution.color }} className="w-fit h-fit p-2 rounded-lg">
                      <Image 
                        src={solution.icon} 
                        alt={solution.title} 
                        width={40} 
                        height={40} 
                        className="object-contain w-full h-[14px]"
                      />
                    </div>
                  </div>
                </div>
                <h3 className="text-lg font-medium mb-1 text-gray-900">
                  {solution.title}
                </h3>
                <p className="text-gray-600 text-md">{solution.content}</p>
              </div>
            ))}
          
          </div>
        </div>
      </div>

     
    </div>
  );
};

export default WorkflowsPage;
