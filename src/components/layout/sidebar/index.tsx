"use client";

import { <PERSON>, Bookmark, CircleX, PinIcon } from "lucide-react";
import React, { useContext, useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import Image from "next/image";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import {
  AgentsIcon,
  BellIcon,
  DMsIcon,
  GearIcon,
  HomeIcon,
  PeopleIcon,
  PlusIcon,
  SidebarPlusIcon,
} from "~/svgs";
import {
  DeleteRequest,
  GetRequest,
  PostRequest,
  PutRequest,
} from "~/utils/new-request";
import Loading from "~/components/ui/loading";
import { getInitials, orderResponseAlphabetically } from "~/utils/utils";
import UseFirstChannel from "~/app/(client)/client/home/<USER>/hooks/first-channel";
import ProfileDropdown from "~/app/(client)/client/_components/profile-dropdown";
import UseHomeChannel from "~/app/(client)/client/home/<USER>/hooks/home-channels";
import cogoToast from "cogo-toast";

//

const SideBar: React.FC = () => {
  const pathname = usePathname();
  const { state, dispatch } = useContext(DataContext);
  const { user } = state;
  const [organisations, setOrganisations] = useState<any>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [orgData, setOrgData] = useState<any>(null);
  const [orgloading, setOrgloading] = useState(true);
  const channelId = localStorage.getItem("channelId") || "";
  const { firstChannel } = UseFirstChannel();
  const [pinnedOrg, setPinnedOrg] = useState<any>([]);
  const [pinnedCallback, setPinnedCallback] = useState(false);

  useEffect(() => {
    const org_id = localStorage.getItem("orgId") || "";

    const fetchOrg = async () => {
      const res = await GetRequest("/users/organisations");
      if (res?.status === 200 || res?.status === 201) {
        const result = orderResponseAlphabetically(res?.data?.data);
        setOrganisations(result);

        // after getting organisations - get user role in that organisation
        getRole();

        // find through the data to get the organisation with the id
        const org = res?.data?.data?.find((item: any) => item.id === org_id);
        setOrgData(org);
        dispatch({ type: ACTIONS.ORG_DATA, payload: org });
        dispatch({ type: ACTIONS.ORG_ID, payload: org?.id });
        localStorage.setItem("orgId", org?.id);
        setOrgloading(false);
      }
    };

    fetchOrg();
  }, [state?.callback, pinnedCallback]);

  // get pinned org from db
  useEffect(() => {
    const getPinnedOrg = async () => {
      const res = await GetRequest(`/organisations/pin`);
      if (res?.status === 200 || res?.status === 201) {
        setPinnedOrg(res.data.data);
      }
    };
    getPinnedOrg();
  }, [pinnedCallback]);

  // fetch user role in an organisation
  const getRole = async () => {
    const orgId = localStorage.getItem("orgId") || "";
    const user = JSON.parse(localStorage.getItem("user") || "{}");

    const res = await GetRequest(
      `/users/${user.id}/organisations/${orgId}/roles`
    );

    if (res?.status === 200 || res?.status === 201) {
      dispatch({ type: ACTIONS.ROLE, payload: res?.data?.data?.role_name });
      localStorage.setItem("role", res?.data?.data?.role_name);
    }
  };

  // switch handle org
  const handleOrg = async (item: any) => {
    localStorage.removeItem("channelId");

    dispatch({ type: ACTIONS.LOADING, payload: true });

    const payload = {
      current_org: item?.id,
    };

    const res = await PutRequest("/users/switch-org", payload);

    if (res?.status === 200 || res?.status === 201) {
      localStorage.setItem("token", res?.data?.data?.access_token);
      localStorage.setItem("orgId", res?.data?.data?.organisation?.id);
      dispatch({
        type: ACTIONS.ORG_ID,
        payload: res?.data?.data?.organisation?.id,
      });

      // get the first channel
      const channelId = await firstChannel(res?.data?.data?.organisation?.id);

      setIsDropdownOpen(false);
      dispatch({ type: ACTIONS.LOADING, payload: false });

      // after switching org, get user role in that org
      getRole();
      if (channelId) {
        window.location.href = `/client/home/<USER>/${channelId}`;
      } else {
        window.location.href = `/client`;
      }
    } else {
      dispatch({ type: ACTIONS.LOADING, payload: false });
    }
  };

  const switchOrg = async (item: any) => {
    localStorage.removeItem("channelId");

    dispatch({ type: ACTIONS.LOADING, payload: true });

    const payload = {
      current_org: item?.org_id,
    };

    const res = await PutRequest("/users/switch-org", payload);

    if (res?.status === 200 || res?.status === 201) {
      localStorage.setItem("token", res?.data?.data?.access_token);
      localStorage.setItem("orgId", res?.data?.data?.organisation?.id);
      dispatch({
        type: ACTIONS.ORG_ID,
        payload: res?.data?.data?.organisation?.id,
      });

      // get the first channel
      const channelId = await firstChannel(res?.data?.data?.organisation?.id);

      setIsDropdownOpen(false);
      dispatch({ type: ACTIONS.LOADING, payload: false });

      // after switching org, get user role in that org
      getRole();
      window.location.href = `/client/home/<USER>/${channelId}`;
    } else {
      dispatch({ type: ACTIONS.LOADING, payload: false });
    }
  };

  // pin organisations
  const handlePin = async (id: string, e: any) => {
    e.stopPropagation();

    const payload = {
      org_id: id,
    };

    const res = await PostRequest("/organisations/pin", payload);
    if (res?.status === 200 || res?.status === 201) {
      setPinnedCallback(!pinnedCallback);
      cogoToast.success(res.data.message);
    }
  };

  const handleDelete = async (id: string, e: any) => {
    e.stopPropagation();
    const res = await DeleteRequest(`/organisations/pin/${id}`);
    if (res?.status === 200 || res?.status === 201) {
      setPinnedCallback(!pinnedCallback);
      cogoToast.success(res.data.message);
    }
  };

  // set channel name to empty string
  const searchValue = () => {
    localStorage.setItem("channelName", "");
    dispatch({ type: ACTIONS.CHANNEL_BAR, payload: false });
  };

  //

  return (
    <>
      <UseHomeChannel />
      <div
        className={`fixed bg-primary-200 top-[40px] z-30 lg:translate-x-0 transition-transform duration-300 ease-in-out
               ${state?.openSidebar === true ? "translate-x-0" : "-translate-x-full"}`}
      >
        <div className="flex h-[calc(100vh-30px)]" onClick={searchValue}>
          <div
            className={`bg-blue-500 w-[65px] flex flex-col justify-start pb-8`}
          >
            <div
              className={`h-full w-full text-[#344054] rounded-[12px] z-auto flex flex-col justify-between ${pathname?.includes("/welcome") ? "invisible" : ""}`}
            >
              <div className="relative flex flex-col items-center justify-center gap-5 mt-4">
                <div className="flex items-center justify-center p-2 relative">
                  <DropdownMenu
                    open={isDropdownOpen}
                    onOpenChange={setIsDropdownOpen}
                  >
                    <DropdownMenuTrigger asChild>
                      <div className="h-[42px] w-[42px] flex items-center justify-center border border-border shadow-md bg-white rounded-sm cursor-pointer">
                        {orgloading ? (
                          <Loading width="25" height="25" color="#7b50fb" />
                        ) : (
                          <div className="h-[35px] w-[35px] rounded-sm border flex items-center justify-center">
                            {orgData?.logo_url ? (
                              <Image
                                src={orgData?.logo_url}
                                width={35}
                                height={35}
                                alt=""
                                className="h-[35px] w-[35px] rounded-sm"
                                // unoptimized
                              />
                            ) : (
                              <h3 className="text-primary-500 font-bold text-sm">
                                {getInitials(orgData?.name)}
                              </h3>
                            )}
                          </div>
                        )}
                      </div>
                    </DropdownMenuTrigger>

                    {!pathname.includes("/client/welcome") &&
                      !pathname.includes("/client/invited") && (
                        <DropdownMenuContent className="ml-6 p-0 rounded-xl w-[300px] cursor-pointer">
                          <div className="max-h-[400px] overflow-y-auto">
                            {organisations?.map((item: any, index: number) => {
                              return (
                                <DropdownMenuItem
                                  key={index}
                                  className="hover:bg-[#f2f4f7] relative w-full cursor-pointer rounded-xs px-4 py-3"
                                  onClick={() => handleOrg(item)}
                                >
                                  <div className="flex w-full items-center gap-3">
                                    <div
                                      className={`h-[40px] w-[40px] rounded-md border p-[5px] flex items-center justify-center ${item?.id === orgData?.id ? "border-2 border-blue-200" : ""}`}
                                    >
                                      {item?.logo_url ? (
                                        <Image
                                          src={item?.logo_url}
                                          width={35}
                                          height={35}
                                          alt={""}
                                          className="h-full w-full rounded-sm"
                                          // unoptimized
                                        />
                                      ) : (
                                        <h3 className="text-primary-500 font-bold text-base">
                                          {getInitials(item?.name)}
                                        </h3>
                                      )}
                                    </div>
                                    <div>
                                      <h3 className="text-[15px] mb-[1px] capitalize">
                                        {item?.name}
                                      </h3>
                                    </div>

                                    <div
                                      onClick={(e) =>
                                        item?.pinned
                                          ? handleDelete(item?.id, e)
                                          : handlePin(item?.id, e)
                                      }
                                      className={`absolute top-6 right-5 z-10 hover:bg-gray-300 p-1 rounded-lg ${item?.pinned ? "bg-gray-300" : ""}`}
                                    >
                                      <PinIcon size={15} />
                                    </div>
                                  </div>
                                </DropdownMenuItem>
                              );
                            })}
                          </div>

                          <hr />

                          <Link href={`/client/organization/create`}>
                            <div
                              className="sticky bottom-0 bg-white w-full p-4 hover:bg-[#f2f4f7]"
                              onClick={() => {
                                setIsDropdownOpen(false);
                              }}
                            >
                              <div className="flex items-center gap-3">
                                <PlusIcon />
                                <h3 className="text-[15px] mb-0">
                                  Add an organization
                                </h3>
                              </div>
                            </div>
                          </Link>
                        </DropdownMenuContent>
                      )}
                  </DropdownMenu>
                </div>

                {!pathname.includes("/client/welcome") &&
                  !pathname.includes("/client/invited") && (
                    <Link
                      href={`/client/organization/create`}
                      className="relative cursor-pointer"
                    >
                      <SidebarPlusIcon />
                    </Link>
                  )}

                {pinnedOrg?.map((item: any, index: number) => {
                  return (
                    <div
                      key={index}
                      className="group relative h-[35px] w-[35px] flex items-center justify-center bg-white rounded-sm cursor-pointer"
                      onClick={() => switchOrg(item)}
                    >
                      <div className="h-[35px] w-[35px] rounded-sm flex items-center justify-center">
                        {item?.avatar_url ? (
                          <Image
                            src={item?.avatar_url}
                            width={35}
                            height={35}
                            alt=""
                            className="h-[35px] w-[35px] rounded-sm"
                            unoptimized
                          />
                        ) : (
                          <h3 className="text-primary-500 font-bold text-sm">
                            {getInitials(item?.org_name)}
                          </h3>
                        )}
                      </div>

                      {/* Delete Pin (only visible on hover) */}
                      <div
                        className="absolute -right-2 -top-2 cursor-pointer bg-white rounded-full hidden group-hover:flex items-center justify-center p-1 shadow"
                        onClick={(e) => {
                          handleDelete(item?.org_id, e);
                        }}
                      >
                        <Ban color="red" size={12} />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="relative flex flex-col items-center">
              <Link
                href={`/client/notifications`}
                className={`flex flex-col group z-50 mb-1 cursor-pointer items-center justify-center w-full ${
                  pathname === "/client/notifications"
                    ? "font-medium scale-[1.1]"
                    : "hover:font-medium "
                } `}
              >
                <span
                  className={`p-[8px] rounded-[7px]  ${
                    pathname?.includes("/client/notifications")
                      ? "bg-blue-200"
                      : "group-hover:bg-blue-200"
                  }`}
                >
                  <BellIcon />
                </span>
              </Link>

              <Link
                href={`/client/settings/personal/account`}
                className={`flex flex-col group z-50 mb-1 cursor-pointer items-center justify-center w-full ${
                  pathname === "/client/settings"
                    ? "font-medium scale-[1.1]"
                    : "hover:font-medium "
                } `}
              >
                <span
                  className={`p-[8px] rounded-[7px]  ${
                    pathname?.includes("/client/settings")
                      ? "bg-blue-200"
                      : "group-hover:bg-blue-200"
                  }`}
                >
                  <GearIcon />
                </span>
              </Link>

              <ProfileDropdown user={user} />
            </div>
          </div>

          <div className={`bg-blue-400 w-[80px] flex flex-col justify-start`}>
            {!pathname.includes("/client/welcome") &&
              !pathname.includes("/client/invited") && (
                <div className="h-full w-full text-[#344054] rounded-[12px] z-auto flex flex-col justify-between">
                  <div className="relative">
                    {
                      <CircleX
                        className="md:hidden absolute right-0 top-0"
                        onClick={() =>
                          dispatch({
                            type: ACTIONS.OPEN_SIDEBAR,
                            payload: false,
                          })
                        }
                      />
                    }

                    <div
                      className="flex flex-col items-center mt-[20px] w-full text-[12px]"
                      onClick={() =>
                        dispatch({ type: ACTIONS.OPEN_SIDEBAR, payload: false })
                      }
                    >
                      <Link
                        href={
                          channelId
                            ? `/client/home/<USER>/${channelId}`
                            : "/client"
                        }
                        className={`flex flex-col group mb-2 items-center justify-center space-y-1 w-full p-[5px] ${
                          pathname === "/client/home"
                            ? "font-medium scale-[1.1]"
                            : "hover:font-medium "
                        } `}
                      >
                        <span
                          className={`p-[8px] rounded-[7px]  ${
                            pathname === "/client" ||
                            pathname?.includes("/home")
                              ? "bg-blue-200"
                              : "group-hover:bg-blue-200"
                          }`}
                        >
                          <HomeIcon />
                        </span>
                        <span className="text-white">Home</span>
                      </Link>

                      <Link
                        href={`/client/dm`}
                        className={`flex flex-col group mb-2 items-center justify-center space-y-1 w-full p-[5px] ${
                          pathname === "/client"
                            ? "font-medium scale-[1.1]"
                            : "hover:font-medium "
                        } `}
                      >
                        <span
                          className={`p-[8px] rounded-[7px]  ${
                            pathname?.includes("/client/dm")
                              ? "bg-blue-200"
                              : "group-hover:bg-blue-200"
                          }`}
                        >
                          <DMsIcon />
                        </span>
                        <span className="text-white">DMs</span>
                      </Link>

                      <Link
                        href={`/client/people`}
                        className={`flex flex-col group mb-2 items-center justify-center space-y-1 w-full p-[5px] ${
                          pathname?.includes("/client/people")
                            ? "font-medium scale-[1.1]"
                            : "hover:font-medium "
                        } `}
                      >
                        <span
                          className={`p-[8px] rounded-[7px]  ${
                            pathname?.includes("/client/people")
                              ? "bg-blue-200"
                              : "group-hover:bg-blue-200"
                          }`}
                        >
                          <PeopleIcon />
                        </span>
                        <span className="text-white">People</span>
                      </Link>

                      <Link
                        href={`/client/agents`}
                        className={`flex flex-col group mb-2 items-center justify-center space-y-1 w-full p-[5px] ${
                          pathname === "/client/agents"
                            ? "font-medium scale-[1.1]"
                            : "hover:font-medium "
                        } `}
                      >
                        <span
                          className={`p-[8px] rounded-[7px]  ${
                            pathname?.includes("/client/agents")
                              ? "bg-blue-200"
                              : "group-hover:bg-blue-200"
                          }`}
                        >
                          <AgentsIcon />
                        </span>
                        <span className="text-white">Agents</span>
                      </Link>

                      <Link
                        href={`/client/later`}
                        className={`flex flex-col group mb-2 items-center justify-center space-y-1 w-full p-[5px] ${
                          pathname === "/client/later"
                            ? "font-medium scale-[1.1]"
                            : "hover:font-medium "
                        } `}
                      >
                        <span
                          className={`p-[8px] rounded-[7px]  ${
                            pathname?.includes("/client/later")
                              ? "bg-blue-200"
                              : "group-hover:bg-blue-200"
                          }`}
                        >
                          <Bookmark color="white" size={18} />
                        </span>
                        <span className="text-white">Later</span>
                      </Link>
                    </div>
                  </div>
                </div>
              )}
          </div>
        </div>
      </div>
    </>
  );
};

export default SideBar;
