"use client";
import { useContext, useEffect, useRef } from "react";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import { ChatBubbleIcon } from "~/svgs";
import { cn } from "~/lib/utils";
import OrganisationMenu from "~/app/(client)/client/_components/org-dropdown";

//

export default function NotificationNav() {
  const { state, dispatch } = useContext(DataContext);
  const sidebarRef = useRef<HTMLDivElement | null>(null);

  // Restore scroll position after re-render
  useEffect(() => {
    const savedScroll = sessionStorage.getItem("sidebar-scroll");
    if (sidebarRef.current && savedScroll) {
      sidebarRef.current.scrollTop = parseInt(savedScroll, 10);
    }
  }, []);

  // Save scroll position before re-render
  const handleScroll = () => {
    if (sidebarRef.current) {
      sessionStorage.setItem(
        "sidebar-scroll",
        sidebarRef.current.scrollTop.toString()
      );
    }
  };

  //

  return (
    <>
      <div
        className={`fixed top-[60px] lg:left-[145px] h-[calc(100vh-60px)] bg-blue-300 lg:translate-x-0 ${state?.channelBar === true && state?.openSidebar ? "translate-x-[145px]" : state?.channelBar === true && !state?.openSidebar ? "translate-x-0" : "-translate-x-full "}
      pt-4 flex flex-col gap-4 sm:w-[350px] transition-transform duration-300 ease-in-out z-20`}
      >
        <div className="flex items-center justify-between px-4 ">
          <OrganisationMenu name="Notifications" />
        </div>

        <div
          className="overflow-auto [&::-webkit-scrollbar]:hidden text-blue-50 cursor-pointer"
          ref={sidebarRef}
          onScroll={handleScroll}
        >
          {state?.notifications
            ?.slice(0, 0)
            ?.map((item: any, index: number) => {
              return (
                <div
                  key={index}
                  onClick={() =>
                    dispatch({
                      type: ACTIONS.NOTIFICATION_DETAIL,
                      payload: item,
                    })
                  }
                  className="flex-1 flex items-center gap-[2px] py-2 px-2 mt-1 mx-2 hover:bg-blue-200 hover:text-white rounded-lg "
                >
                  <ChatBubbleIcon />
                  <p
                    className={cn(
                      "text-[15px] leading-4 truncate w-[180px] text-blue-50"
                    )}
                  >
                    {item?.data?.description || item?.data?.full_name}
                  </p>
                </div>
              );
            })}

          {/* {state?.notifications?.length === 0 && ( */}
          <p className="text-center mt-10">No recent notifications</p>
          {/* )} */}
        </div>
      </div>
    </>
  );
}
