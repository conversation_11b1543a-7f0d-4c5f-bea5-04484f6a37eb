"use client";

import { useContext, useEffect, useRef, useState } from "react";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import { Search, X } from "lucide-react";
import { PencilIcon } from "~/svgs";
import { Input } from "~/components/ui/input";
import Image from "next/image";
import images from "~/assets/images";
import { useParams, useRouter } from "next/navigation";
import { search } from "~/utils/filter";
import OrganisationMenu from "~/app/(client)/client/_components/org-dropdown";
import { PostRequest } from "~/utils/new-request";
import { formatCount } from "~/utils/utils";
import { cn } from "~/lib/utils";

//

export default function PeopleNav() {
  const { state, dispatch } = useContext(DataContext);
  const [searchInput, setSearchInput] = useState("");
  const sidebarRef = useRef<HTMLDivElement | null>(null);
  const memberRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const params = useParams();
  const id2 = params.id2 as string;
  const router = useRouter();

  // Restore scroll position after re-render
  useEffect(() => {
    const savedScroll = sessionStorage.getItem("sidebar-scroll");
    if (sidebarRef.current && savedScroll) {
      sidebarRef.current.scrollTop = parseInt(savedScroll, 10);
    }
  }, []);

  // Save scroll position before re-render
  const handleScroll = () => {
    if (sidebarRef.current) {
      sessionStorage.setItem(
        "sidebar-scroll",
        sidebarRef.current.scrollTop.toString()
      );
    }
  };

  const handleRoute = async (data: any) => {
    localStorage.setItem("channelName", data?.name);

    const orgId = localStorage.getItem("orgId") || "";

    const payload = {
      chat_type: data?.entity_type,
      participant_id: data?.id,
    };

    const res = await PostRequest(`/organisations/${orgId}/dms`, payload);

    if (res?.status === 200 || res?.status === 201) {
      router.push(
        `/client/people/${res?.data?.data?.channel_id}/${res?.data?.data?.participant_id}`
      );
    }
  };

  const handleNewChat = () => {
    dispatch({ type: ACTIONS.CLEAR_CHATS });
    router.push("/client/home/<USER>/new-chat");
  };

  const members = state?.orgMembers?.filter(
    (item: any) => item.entity_type === "user"
  );

  const searchData = search(members, searchInput);

  useEffect(() => {
    if (id2 && memberRefs.current[id2]) {
      memberRefs.current[id2]?.scrollIntoView({
        behavior: "auto",
        block: "start",
      });
    }
  }, [id2, searchData]);
  //

  return (
    <>
      <div
        className={`fixed top-[60px] lg:left-[145px] h-[calc(100vh-60px)] bg-blue-300 lg:translate-x-0 ${state?.channelBar === true && state?.openSidebar ? "translate-x-[145px]" : state?.channelBar === true && !state?.openSidebar ? "translate-x-0" : "-translate-x-full "}
      pt-4 flex flex-col gap-4 sm:w-[350px] transition-transform duration-300 ease-in-out z-20`}
      >
        <div className="flex items-center justify-between px-3 ">
          <div className="flex items-center gap-[5px] md:justify-between w-full">
            <OrganisationMenu name="People" />

            <button type="button" onClick={handleNewChat}>
              <PencilIcon />
            </button>
          </div>
        </div>

        {/* search bar */}
        <div className="relative h-fit w-full px-3">
          <Input
            type="search"
            placeholder="Find a conversation"
            className="h-10 pl-8 pr-8 bg-[#4A4AAF] border-[#5F5FE1] text-white placeholder:text-white/80"
            onChange={(e) => setSearchInput(e.target.value)}
            value={searchInput}
          />

          <div className="absolute top-[12px] left-6">
            <Search color="#FFFFFF" size={16} />
          </div>

          {searchInput && (
            <button
              type="button"
              className="absolute top-[12px] right-5 text-white"
              onClick={() => setSearchInput("")}
            >
              <X size={16} />
            </button>
          )}
        </div>

        <div
          className="overflow-auto [&::-webkit-scrollbar]:hidden text-blue-50 cursor-pointer"
          ref={sidebarRef}
          onScroll={handleScroll}
          onClick={() =>
            dispatch({ type: ACTIONS.CHANNEL_BAR, payload: false })
          }
        >
          <div className="flex flex-col">
            {searchData?.map((dm: any, index: number) => {
              return (
                <>
                  <div
                    key={dm?.id}
                    ref={(el) => {
                      memberRefs.current[dm?.id] = el;
                    }}
                    onClick={() => handleRoute(dm)}
                    className={`flex items-center gap-3 px-3 py-4 hover:bg-[#4B4BB4] ${dm?.id === id2 ? "bg-[#4B4BB4]" : ""}`}
                  >
                    <div className="relative size-6 bg-white rounded-[7px]">
                      <Image
                        width={36}
                        height={36}
                        src={
                          dm?.profile_url
                            ? dm?.profile_url
                            : dm?.entity_type == "user" ||
                                dm?.entity_type === ""
                              ? images?.user
                              : images?.bot
                        }
                        className="rounded-[7px] size-6 object-cover"
                        alt={dm?.name}
                      />
                      <span
                        className={`absolute -right-1 -bottom-1 ${dm?.isOnline ? "bg-[#00AD51]" : "bg-[#F97316]"} w-[8px] h-[8px] rounded-full border border-white`}
                      />
                    </div>

                    <div className="-mt-1">
                      <span className="text-[15px] font-semibold text-white break-words max-w-[300px] line-clamp-2">
                        {dm?.name}
                      </span>

                      {/* <p className="text-[14px] text-[#D0D0FD] break-words max-w-[300px] line-clamp-2">
                        {dm?.email}
                      </p> */}
                    </div>

                    {dm.thread_count > 0 && (
                      <div
                        className={cn(
                          `absolute right-3 flex items-center justify-center rounded-full bg-blue-200 hover:bg-blue-500 text-white tracking-[-0.5%] font-bold text-right text-[10px] px-2`
                          // isSelected ? "bg-blue-500 text-white" : ""
                        )}
                      >
                        {formatCount(dm?.thread_count || 0)}
                      </div>
                    )}
                  </div>

                  {index !== state?.dms.length - 1 && (
                    <hr className="border-[#5F5FE1]" />
                  )}
                </>
              );
            })}

            {(state?.orgMembers?.length === 0 || searchData?.length === 0) && (
              <p className="self-center mt-10">No available member</p>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
