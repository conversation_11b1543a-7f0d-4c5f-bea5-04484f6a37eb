"use client";
import { useContext, useEffect, useRef, useState } from "react";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { BrowseIcon, DropdownIcon } from "~/svgs";
import Link from "next/link";
import { usePathname } from "next/navigation";
import OrganisationMenu from "~/app/(client)/client/_components/org-dropdown";
import { WorkflowCard } from "~/app/(client)/client/_components/workflows/workflow-card";

//

export default function WorkflowNav() {
  const { state, dispatch } = useContext(DataContext);
  const pathname = usePathname();
  const sidebarRef = useRef<HTMLDivElement | null>(null);

  // Restore scroll position after re-render
  useEffect(() => {
    const savedScroll = sessionStorage.getItem("sidebar-scroll");
    if (sidebarRef.current && savedScroll) {
      sidebarRef.current.scrollTop = parseInt(savedScroll, 10);
    }
  }, []);

  // Save scroll position before re-render
  const handleScroll = () => {
    if (sidebarRef.current) {
      sessionStorage.setItem(
        "sidebar-scroll",
        sidebarRef.current.scrollTop.toString()
      );
    }
  };

  const [openAccordions, setOpenAccordions] = useState<string[]>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("openAccordions3");
      return saved ? JSON.parse(saved) : ["activated"];
    }
    return ["activated"];
  });

  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const stored = localStorage.getItem("openAccordions3");
      setOpenAccordions(JSON.parse(stored || '["activated", "suggestions"]'));
      setIsHydrated(true);
    }
  }, []);

  // Function to toggle accordions independently
  const handleAccordionChanges = (values: string[]) => {
    setOpenAccordions(values);
    if (typeof window !== "undefined") {
      localStorage.setItem("openAccordions3", JSON.stringify(values));
    }
  };

  //

  return (
    <>
      <div
        className={`fixed top-[60px] lg:left-[145px] h-[calc(100vh-60px)] bg-blue-300 lg:translate-x-0 ${state?.channelBar === true && state?.openSidebar ? "translate-x-[145px]" : state?.channelBar === true && !state?.openSidebar ? "translate-x-0" : "-translate-x-full "}
      pt-4 flex flex-col gap-4 sm:w-[350px] transition-transform duration-300 ease-in-out z-20`}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center px-3 gap-[5px] md:justify-between w-full">
            <OrganisationMenu name="Workflows" />

            <Link
              href={`/client/workflows/browse-workflows`}
              className={`text-blue-50 flex items-center text-[13px] gap-2 ${pathname === "/client/workflows/browse-workflows" ? "bg-blue-200 p-3 py-2 rounded text-white" : ""}`}
            >
              Browse Workflows
              <BrowseIcon
                active={
                  pathname === "/client/workflows/browse-workflows"
                    ? true
                    : false
                }
              />
            </Link>
          </div>
        </div>

        <div
          className="overflow-auto [&::-webkit-scrollbar]:hidden text-blue-50 cursor-pointer"
          ref={sidebarRef}
          onScroll={handleScroll}
          onClick={() =>
            dispatch({ type: ACTIONS.CHANNEL_BAR, payload: false })
          }
        >
          <div className="">
            {/* Agents */}
            {isHydrated && (
              <Accordion
                type="multiple"
                className="w-full"
                value={openAccordions}
                onValueChange={handleAccordionChanges}
              >
                <AccordionItem value="activated" className="border-none">
                  <AccordionTrigger className="font-normal w-full py-0">
                    <div className="relative py-3 mx-4 flex items-center gap-1 rounded-lg cursor-pointer w-full">
                      <DropdownIcon
                        className={`w-5 h-5 transition-transform duration-300 ${
                          openAccordions.includes("activated")
                            ? "rotate-0"
                            : "-rotate-90"
                        }`}
                      />
                      <h3 className="text-[15px]  font-medium">Activated</h3>
                    </div>
                  </AccordionTrigger>

                  <AccordionContent>
                    <ul className="flex flex-col gap-1">
                      {state?.workflows?.map((item: any, index: number) => (
                        <WorkflowCard {...item} key={index} />
                      ))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
