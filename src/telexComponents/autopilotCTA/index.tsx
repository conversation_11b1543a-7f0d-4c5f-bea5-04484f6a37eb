import React from "react";
import Link from "next/link";
import { But<PERSON> } from "~/components/ui/button";
import Image from "next/image";

const AutopilotCTA = () => {
  return (
    <div className='relative bg-[url("/images/industry-ready-bg.jpeg")] bg-cover bg-no-repeat bg-center w-full min-h-[400px] sm:min-h-[400px] lg:min-h-[400px] xl:min-h-[720px] px-4 sm:px-16 lg:px-0 lg:pl-24 pt-8 lg:pt-24 flex items-center justify-center'>
      <div className="absolute inset-0 bg-black/50 z-0"></div>
      <div className="container flex flex-col lg:flex-row items-center lg:items-start justify-center lg:justify-between gap-12 relative z-10">
        {/* Text Content */}
        <div className="w-full lg:w-[484px] max-w-[484px] text-center lg:text-left flex flex-col gap-6">
          <h1 className="text-white text-3xl sm:text-4xl font-semibold leading-tight">
            Ready to scale your business using AI?
          </h1>
          <p className="text-white text-base lg:text-base">
            Save money and time by hiring low-cost Al co-workers to do parts of
            your business. Let your team focus on the things that really matter.
          </p>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-6 justify-center lg:justify-start">
            <Link href="/auth/sign-up">
              <Button className="bg-gradient-to-b from-[#8760f8] to-[#7141f8] text-white py-4 px-6  text-sm sm:text-base">
                Sign Up
              </Button>
            </Link>
            <Link href="/agents">
              <Button className="border border-primary-500 !bg-white text-primary-500 py-4 px-6  text-sm sm:text-base">
                Browse Agents
              </Button>
            </Link>
          </div>
        </div>

        {/* Image */}
        <div className="w-full max-w-[500px] sm:max-w-[600px] lg:max-w-[816px]">
          <Image
            src="/images/industry-ready-view.svg"
            alt="star"
            width={816}
            height={620}
            className="object-contain w-full h-auto rounded-[10px] lg:rounded-none sm:rounded-tl-[10px]"
          />
        </div>
      </div>
    </div>
  );
};

export default AutopilotCTA;
